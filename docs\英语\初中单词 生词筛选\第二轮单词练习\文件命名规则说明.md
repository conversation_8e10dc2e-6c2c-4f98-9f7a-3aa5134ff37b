# 导出文件命名规则说明

## 📝 文件命名格式

### 预设组练习
当练习预设的1-5组单词时，导出的文件名格式为：
```
wrong_words_第X组_YYYY-MM-DD.json
```

#### 示例
- `wrong_words_第1组_2025-07-21.json` - 第1组的错误单词
- `wrong_words_第2组_2025-07-21.json` - 第2组的错误单词
- `wrong_words_第3组_2025-07-21.json` - 第3组的错误单词
- `wrong_words_第4组_2025-07-21.json` - 第4组的错误单词
- `wrong_words_第5组_2025-07-21.json` - 第5组的错误单词

### 自定义导入练习
当导入自定义JSON文件进行练习时，导出的文件名格式为：
```
wrong_words_自定义_YYYY-MM-DD.json
```

#### 示例
- `wrong_words_自定义_2025-07-21.json` - 自定义导入单词的错误记录

## 📊 JSON文件内容增强

### 新增字段
导出的JSON文件现在包含更多元数据：

```json
{
  "exportTime": "2025-07-21T14:30:45.123Z",
  "groupNumber": 1,
  "groupType": "第1组",
  "totalWords": 100,
  "wrongWordsCount": 15,
  "words": [...]
}
```

### 字段说明
- `groupNumber`: 组号（1-5为预设组，0为自定义导入）
- `groupType`: 组类型描述（"第X组" 或 "自定义导入"）
- `totalWords`: 本次练习的总单词数
- `wrongWordsCount`: 错误单词数量
- `exportTime`: 导出时间（ISO格式）

## 🎯 使用优势

### 文件管理
- **清晰识别**：从文件名就能知道是哪一组的错误记录
- **分类整理**：可以按组号对文件进行分类管理
- **时间追踪**：文件名包含日期，便于追踪学习进度

### 学习分析
- **组别对比**：可以比较不同组的错误率
- **进度跟踪**：通过文件名快速了解学习历史
- **重点复习**：针对特定组的错误单词进行复习

### 数据处理
- **批量分析**：程序可以根据文件名自动分类处理
- **元数据丰富**：JSON内容包含完整的练习信息
- **兼容性好**：保持与之前版本的兼容性

## 📁 文件组织建议

### 按组分类
```
错误单词记录/
├── 第1组/
│   ├── wrong_words_第1组_2025-07-20.json
│   ├── wrong_words_第1组_2025-07-21.json
│   └── wrong_words_第1组_2025-07-22.json
├── 第2组/
│   ├── wrong_words_第2组_2025-07-21.json
│   └── wrong_words_第2组_2025-07-22.json
└── 自定义/
    ├── wrong_words_自定义_2025-07-20.json
    └── wrong_words_自定义_2025-07-21.json
```

### 按时间分类
```
错误单词记录/
├── 2025-07-20/
│   ├── wrong_words_第1组_2025-07-20.json
│   └── wrong_words_自定义_2025-07-20.json
├── 2025-07-21/
│   ├── wrong_words_第1组_2025-07-21.json
│   ├── wrong_words_第2组_2025-07-21.json
│   └── wrong_words_第3组_2025-07-21.json
└── 2025-07-22/
    └── wrong_words_第1组_2025-07-22.json
```
