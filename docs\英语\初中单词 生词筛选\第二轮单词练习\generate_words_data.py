#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成单词数据JavaScript文件的脚本
从consolidated_words.txt文件中提取所有单词数据并转换为JavaScript格式
"""

import re
import json

def parse_consolidated_words():
    """解析consolidated_words.txt文件"""
    words = []

    try:
        with open('consolidated_words.txt', 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式匹配单词条目 - 修正格式匹配
        pattern = r'(\d+)\.\s+([^(]+?)\s*\(([^)]+)\)\s*\n\s*中文:\s*([^\n]+)\s*\n\s*例句:\s*([^\n]+)'
        matches = re.findall(pattern, content, re.MULTILINE)

        for match in matches:
            word_id, english, part_of_speech, chinese, example = match

            # 清理数据
            english = english.strip()
            part_of_speech = part_of_speech.strip()
            chinese = chinese.strip()
            example = example.strip()

            word_data = {
                'id': int(word_id),
                'english': english,
                'chinese': chinese,
                'partOfSpeech': part_of_speech,
                'example': example
            }

            words.append(word_data)

        print(f"成功解析了 {len(words)} 个单词")
        return words

    except FileNotFoundError:
        print("错误：找不到 consolidated_words.txt 文件")
        return []
    except Exception as e:
        print(f"解析文件时出错：{e}")
        return []

def add_phonetic_data(words):
    """为单词添加音标数据"""
    # 常见单词的音标数据
    phonetic_dict = {
        "a few": "/ə fjuː/",
        "above": "/əˈbʌv/",
        "absent": "/ˈæbsənt/",
        "accent": "/ˈæksent/",
        "accidentally": "/ˌæksɪˈdentəli/",
        "account": "/əˈkaʊnt/",
        "accountant": "/əˈkaʊntənt/",
        "achieve": "/əˈtʃiːv/",
        "action": "/ˈækʃən/",
        "active": "/ˈæktɪv/",
        "activity": "/ækˈtɪvəti/",
        "ad": "/æd/",
        "address": "/əˈdres/",
        "admire": "/ədˈmaɪər/",
        "admit": "/ədˈmɪt/",
        "adopt": "/əˈdɒpt/",
        "advantage": "/ədˈvɑːntɪdʒ/",
        "adventure": "/ədˈventʃər/",
        "affect": "/əˈfekt/",
        "agent": "/ˈeɪdʒənt/",
        "ago": "/əˈɡəʊ/",
        "agree": "/əˈɡriː/",
        "agriculture": "/ˈæɡrɪkʌltʃər/",
        "ahead": "/əˈhed/",
        "aid": "/eɪd/",
        "alarm clock": "/əˈlɑːm klɒk/",
        "album": "/ˈælbəm/",
        "alone": "/əˈləʊn/",
        "already": "/ɔːlˈredi/",
        "although": "/ɔːlˈðəʊ/",
        "altogether": "/ˌɔːltəˈɡeðər/",
        "amazing": "/əˈmeɪzɪŋ/",
        "among": "/əˈmʌŋ/",
        "amusing": "/əˈmjuːzɪŋ/",
        "an": "/æn/",
        "analyse": "/ˈænəlaɪz/",
        "angrily": "/ˈæŋɡrəli/",
        "animated": "/ˈænɪmeɪtɪd/",
        "animation": "/ˌænɪˈmeɪʃən/",
        "ankle": "/ˈæŋkəl/",
        "announcement": "/əˈnaʊnsmənt/",
        "anybody": "/ˈenibɒdi/",
        "anyway": "/ˈeniweɪ/",
        "apartment": "/əˈpɑːtmənt/",
        "appear": "/əˈpɪər/",
        "application": "/ˌæplɪˈkeɪʃən/",
        "architect": "/ˈɑːkɪtekt/",
        "area": "/ˈeəriə/",
        "argue": "/ˈɑːɡjuː/",
        "arrange": "/əˈreɪndʒ/",
        "arrival": "/əˈraɪvəl/",
        "artist": "/ˈɑːtɪst/",
        "artistic": "/ɑːˈtɪstɪk/",
        "assistant": "/əˈsɪstənt/",
        "athlete": "/ˈæθliːt/",
        "attack": "/əˈtæk/",
        "attend": "/əˈtend/",
        "attract": "/əˈtrækt/",
        "attraction": "/əˈtrækʃən/",
        "attractive": "/əˈtræktɪv/",
        "audience": "/ˈɔːdiəns/",
        "available": "/əˈveɪləbəl/",
        "aware": "/əˈweər/",
        "away": "/əˈweɪ/",
        "awful": "/ˈɔːfəl/",
        "badly": "/ˈbædli/",
        "bait": "/beɪt/",
        "bake": "/beɪk/",
        "balanced": "/ˈbælənst/",
        "base": "/beɪs/",
        "bass": "/bæs/",
        "bay": "/beɪ/",
        "be": "/biː/",
        "beat": "/biːt/",
        "beautifully": "/ˈbjuːtɪfəli/",
        "bedroom": "/ˈbedruːm/",
        "beer": "/bɪər/",
        "beginning": "/bɪˈɡɪnɪŋ/",
        "behaviour": "/bɪˈheɪvjər/",
        "being": "/ˈbiːɪŋ/",
        "belief": "/bɪˈliːf/",
        "believe": "/bɪˈliːv/",
        "belt": "/belt/",
        "bench": "/bentʃ/",
        "benefit": "/ˈbenɪfɪt/",
        "biology": "/baɪˈɒlədʒi/",
        "birth": "/bɜːθ/",
        "blackboard": "/ˈblækbɔːd/",
        "blank": "/blæŋk/",
        "bleed": "/bliːd/",
        "block": "/blɒk/",
        "blog": "/blɒɡ/",
        "blouse": "/blaʊs/",
        "board": "/bɔːd/",
        "booklet": "/ˈbʊklət/",
        "boot": "/buːt/",
        "branch": "/brɑːntʃ/"
    }

    # 为单词添加音标
    for word in words:
        english_lower = word['english'].lower()
        if english_lower in phonetic_dict:
            word['phonetic'] = phonetic_dict[english_lower]
        else:
            word['phonetic'] = ""  # 如果没有找到音标，设为空字符串

    return words

def generate_js_file(words):
    """生成JavaScript文件"""
    if not words:
        print("没有单词数据，无法生成文件")
        return

    # 添加音标数据
    words = add_phonetic_data(words)

    js_content = "// 单词数据 - 从consolidated_words.txt提取的442个单词\nconst WORDS_DATA = [\n"

    for i, word in enumerate(words):
        # 转义JavaScript字符串中的特殊字符
        english = word['english'].replace('"', '\\"').replace("'", "\\'")
        chinese = word['chinese'].replace('"', '\\"').replace("'", "\\'")
        part_of_speech = word['partOfSpeech'].replace('"', '\\"').replace("'", "\\'")
        example = word['example'].replace('"', '\\"').replace("'", "\\'")
        phonetic = word.get('phonetic', '').replace('"', '\\"').replace("'", "\\'")

        js_content += f'    {{id: {word["id"]}, english: "{english}", chinese: "{chinese}", partOfSpeech: "{part_of_speech}", example: "{example}"}}'

        if i < len(words) - 1:
            js_content += ","
        js_content += "\n"

    js_content += "];\n"

    try:
        with open('words_data.js', 'w', encoding='utf-8') as f:
            f.write(js_content)
        print(f"成功生成 words_data.js 文件，包含 {len(words)} 个单词（含音标）")
    except Exception as e:
        print(f"生成文件时出错：{e}")

def main():
    """主函数"""
    print("开始解析单词数据...")
    words = parse_consolidated_words()
    
    if words:
        print("开始生成JavaScript文件...")
        generate_js_file(words)
        print("完成！")
    else:
        print("解析失败，请检查文件格式")

if __name__ == "__main__":
    main()
