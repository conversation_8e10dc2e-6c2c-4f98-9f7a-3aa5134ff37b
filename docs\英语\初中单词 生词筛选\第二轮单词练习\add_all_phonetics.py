#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为所有442个单词添加音标数据的脚本
"""

import re

def create_complete_phonetic_dict():
    """创建包含所有单词音标的完整字典"""
    return {
        # A
        "a few": "/ə fjuː/",
        "A.M.": "",  # 缩写不需要音标
        "above": "/əˈbʌv/",
        "absent": "/ˈæbsənt/",
        "accent": "/ˈæksent/",
        "accidentally": "/ˌæksɪˈdentəli/",
        "account": "/əˈkaʊnt/",
        "accountant": "/əˈkaʊntənt/",
        "achieve": "/əˈtʃiːv/",
        "action": "/ˈækʃən/",
        "active": "/ˈæktɪv/",
        "activity": "/ækˈtɪvəti/",
        "ad": "/æd/",
        "address": "/əˈdres/",
        "admire": "/ədˈmaɪər/",
        "admit": "/ədˈmɪt/",
        "adopt": "/əˈdɒpt/",
        "advantage": "/ədˈvɑːntɪdʒ/",
        "adventure": "/ədˈventʃər/",
        "affect": "/əˈfekt/",
        "agent": "/ˈeɪdʒənt/",
        "ago": "/əˈɡəʊ/",
        "agree": "/əˈɡriː/",
        "agriculture": "/ˈæɡrɪkʌltʃər/",
        "ahead": "/əˈhed/",
        "aid": "/eɪd/",
        "alarm clock": "/əˈlɑːm klɒk/",
        "album": "/ˈælbəm/",
        "alone": "/əˈləʊn/",
        "already": "/ɔːlˈredi/",
        "although": "/ɔːlˈðəʊ/",
        "altogether": "/ˌɔːltəˈɡeðər/",
        "amazing": "/əˈmeɪzɪŋ/",
        "among": "/əˈmʌŋ/",
        "amusing": "/əˈmjuːzɪŋ/",
        "an": "/æn/",
        "analyse": "/ˈænəlaɪz/",
        "angrily": "/ˈæŋɡrəli/",
        "animated": "/ˈænɪmeɪtɪd/",
        "animation": "/ˌænɪˈmeɪʃən/",
        "ankle": "/ˈæŋkəl/",
        "announcement": "/əˈnaʊnsmənt/",
        "anybody": "/ˈenibɒdi/",
        "anyway": "/ˈeniweɪ/",
        "apartment": "/əˈpɑːtmənt/",
        "appear": "/əˈpɪər/",
        "application": "/ˌæplɪˈkeɪʃən/",
        "architect": "/ˈɑːkɪtekt/",
        "area": "/ˈeəriə/",
        "argue": "/ˈɑːɡjuː/",
        "arrange": "/əˈreɪndʒ/",
        "arrival": "/əˈraɪvəl/",
        "artist": "/ˈɑːtɪst/",
        "artistic": "/ɑːˈtɪstɪk/",
        "assistant": "/əˈsɪstənt/",
        "athlete": "/ˈæθliːt/",
        "attack": "/əˈtæk/",
        "attend": "/əˈtend/",
        "attract": "/əˈtrækt/",
        "attraction": "/əˈtrækʃən/",
        "attractive": "/əˈtræktɪv/",
        "audience": "/ˈɔːdiəns/",
        "available": "/əˈveɪləbəl/",
        "aware": "/əˈweər/",
        "away": "/əˈweɪ/",
        "awful": "/ˈɔːfəl/",
        
        # B
        "badly": "/ˈbædli/",
        "bait": "/beɪt/",
        "bake": "/beɪk/",
        "balanced": "/ˈbælənst/",
        "base": "/beɪs/",
        "bass": "/bæs/",
        "bay": "/beɪ/",
        "be": "/biː/",
        "beat": "/biːt/",
        "beautifully": "/ˈbjuːtɪfəli/",
        "bedroom": "/ˈbedruːm/",
        "beer": "/bɪər/",
        "beginning": "/bɪˈɡɪnɪŋ/",
        "behaviour": "/bɪˈheɪvjər/",
        "being": "/ˈbiːɪŋ/",
        "belief": "/bɪˈliːf/",
        "believe": "/bɪˈliːv/",
        "belt": "/belt/",
        "bench": "/bentʃ/",
        "benefit": "/ˈbenɪfɪt/",
        "biology": "/baɪˈɒlədʒi/",
        "birth": "/bɜːθ/",
        "blackboard": "/ˈblækbɔːd/",
        "blank": "/blæŋk/",
        "bleed": "/bliːd/",
        "block": "/blɒk/",
        "blog": "/blɒɡ/",
        "blouse": "/blaʊs/",
        "board": "/bɔːd/",
        "booklet": "/ˈbʊklət/",
        "boot": "/buːt/",
        "branch": "/brɑːntʃ/",
        "brave": "/breɪv/",
        "break": "/breɪk/",
        "breath": "/breθ/",
        "breathe": "/briːð/",
        "brick": "/brɪk/",
        "bridge": "/brɪdʒ/",
        "brief": "/briːf/",
        "bright": "/braɪt/",
        "brilliant": "/ˈbrɪljənt/",
        "bring": "/brɪŋ/",
        "broad": "/brɔːd/",
        "broken": "/ˈbrəʊkən/",
        "brush": "/brʌʃ/",
        "build": "/bɪld/",
        "building": "/ˈbɪldɪŋ/",
        "burn": "/bɜːn/",
        "business": "/ˈbɪznəs/",
        "busy": "/ˈbɪzi/",
        "button": "/ˈbʌtən/",
        "buy": "/baɪ/",
        
        # C
        "cafe": "/ˈkæfeɪ/",
        "cage": "/keɪdʒ/",
        "cake": "/keɪk/",
        "calculate": "/ˈkælkjuleɪt/",
        "call": "/kɔːl/",
        "calm": "/kɑːm/",
        "camera": "/ˈkæmərə/",
        "camp": "/kæmp/",
        "can": "/kæn/",
        "cancel": "/ˈkænsəl/",
        "candle": "/ˈkændəl/",
        "cap": "/kæp/",
        "capital": "/ˈkæpɪtəl/",
        "captain": "/ˈkæptɪn/",
        "car": "/kɑːr/",
        "card": "/kɑːrd/",
        "care": "/keər/",
        "career": "/kəˈrɪər/",
        "careful": "/ˈkeərfəl/",
        "carefully": "/ˈkeərfəli/",
        "careless": "/ˈkeərləs/",
        "carry": "/ˈkæri/",
        "case": "/keɪs/",
        "cash": "/kæʃ/",
        "castle": "/ˈkæsəl/",
        "cat": "/kæt/",
        "catch": "/kætʃ/",
        "cause": "/kɔːz/",
        "cave": "/keɪv/",
        "CD": "/ˌsiː ˈdiː/",
        "celebrate": "/ˈseləbreɪt/",
        "cell": "/sel/",
        "cent": "/sent/",
        "center": "/ˈsentər/",
        "central": "/ˈsentrəl/",
        "century": "/ˈsentʃəri/",
        "certain": "/ˈsɜːrtən/",
        "certainly": "/ˈsɜːrtənli/",
        "chain": "/tʃeɪn/",
        "chair": "/tʃeər/",
        "chairman": "/ˈtʃeərmən/",
        "challenge": "/ˈtʃælɪndʒ/",
        "chance": "/tʃæns/",
        "change": "/tʃeɪndʒ/",
        "channel": "/ˈtʃænəl/",
        "chapter": "/ˈtʃæptər/",
        "character": "/ˈkærəktər/",
        "charge": "/tʃɑːrdʒ/",
        "charity": "/ˈtʃærəti/",
        "chart": "/tʃɑːrt/",
        "chat": "/tʃæt/",
        "cheap": "/tʃiːp/",
        "check": "/tʃek/",
        "cheese": "/tʃiːz/",
        "chemistry": "/ˈkemɪstri/",
        "chest": "/tʃest/",
        "chicken": "/ˈtʃɪkən/",
        "child": "/tʃaɪld/",
        "childhood": "/ˈtʃaɪldhʊd/",
        "children": "/ˈtʃɪldrən/",
        "choice": "/tʃɔɪs/",
        "choose": "/tʃuːz/",
        "Christmas": "/ˈkrɪsməs/",
        "church": "/tʃɜːrtʃ/",
        "cinema": "/ˈsɪnəmə/",
        "circle": "/ˈsɜːrkəl/",
        "citizen": "/ˈsɪtɪzən/",
        "city": "/ˈsɪti/",
        "civil": "/ˈsɪvəl/",
        "class": "/klæs/",
        "classic": "/ˈklæsɪk/",
        "classroom": "/ˈklæsruːm/",
        "clean": "/kliːn/",
        "clear": "/klɪər/",
        "clearly": "/ˈklɪərli/",
        "clever": "/ˈklevər/",
        "click": "/klɪk/",
        "climate": "/ˈklaɪmət/",
        "climb": "/klaɪm/",
        "clock": "/klɒk/",
        "close": "/kləʊs/",
        "closed": "/kləʊzd/",
        "closely": "/ˈkləʊsli/",
        "clothes": "/kləʊðz/",
        "cloud": "/klaʊd/",
        "cloudy": "/ˈklaʊdi/",
        "club": "/klʌb/",
        "coach": "/kəʊtʃ/",
        "coal": "/kəʊl/",
        "coast": "/kəʊst/",
        "coat": "/kəʊt/",
        "code": "/kəʊd/",
        "coffee": "/ˈkɒfi/",
        "coin": "/kɔɪn/",
        "cold": "/kəʊld/",
        "collect": "/kəˈlekt/",
        "collection": "/kəˈlekʃən/",
        "college": "/ˈkɒlɪdʒ/",
        "color": "/ˈkʌlər/",
        "colorful": "/ˈkʌlərfəl/",
        "come": "/kʌm/",
        "comedy": "/ˈkɒmədi/",
        "comfort": "/ˈkʌmfərt/",
        "comfortable": "/ˈkʌmfərtəbəl/",
        "command": "/kəˈmænd/",
        "comment": "/ˈkɒment/",
        "common": "/ˈkɒmən/",
        "communicate": "/kəˈmjuːnɪkeɪt/",
        "communication": "/kəˌmjuːnɪˈkeɪʃən/",
        "community": "/kəˈmjuːnəti/",
        "company": "/ˈkʌmpəni/",
        "compare": "/kəmˈpeər/",
        "compete": "/kəmˈpiːt/",
        "competition": "/ˌkɒmpəˈtɪʃən/",
        "complete": "/kəmˈpliːt/",
        "completely": "/kəmˈpliːtli/",
        "computer": "/kəmˈpjuːtər/",
        "concentrate": "/ˈkɒnsəntreɪt/",
        "concept": "/ˈkɒnsept/",
        "concern": "/kənˈsɜːrn/",
        "concert": "/ˈkɒnsərt/",
        "condition": "/kənˈdɪʃən/",
        "conference": "/ˈkɒnfərəns/",
        "confident": "/ˈkɒnfɪdənt/",
        "confirm": "/kənˈfɜːrm/",
        "confused": "/kənˈfjuːzd/",
        "connect": "/kəˈnekt/",
        "connection": "/kəˈnekʃən/",
        "consider": "/kənˈsɪdər/",
        "contact": "/ˈkɒntækt/",
        "contain": "/kənˈteɪn/",
        "content": "/ˈkɒntent/",
        "contest": "/ˈkɒntest/",
        "continue": "/kənˈtɪnjuː/",
        "control": "/kənˈtrəʊl/",
        "conversation": "/ˌkɒnvərˈseɪʃən/",
        "cook": "/kʊk/",
        "cookie": "/ˈkʊki/",
        "cool": "/kuːl/",
        "copy": "/ˈkɒpi/",
        "corn": "/kɔːrn/",
        "corner": "/ˈkɔːrnər/",
        "correct": "/kəˈrekt/",
        "cost": "/kɒst/",
        "cotton": "/ˈkɒtən/",
        "could": "/kʊd/",
        "count": "/kaʊnt/",
        "country": "/ˈkʌntri/",
        "couple": "/ˈkʌpəl/",
        "courage": "/ˈkɜːrɪdʒ/",
        "course": "/kɔːrs/",
        "court": "/kɔːrt/",
        "cover": "/ˈkʌvər/",
        "cow": "/kaʊ/",
        "crazy": "/ˈkreɪzi/",
        "cream": "/kriːm/",
        "create": "/kriˈeɪt/",
        "creative": "/kriˈeɪtɪv/",
        "credit": "/ˈkredɪt/",
        "crime": "/kraɪm/",
        "cross": "/krɒs/",
        "crowd": "/kraʊd/",
        "cruel": "/ˈkruːəl/",
        "cry": "/kraɪ/",
        "culture": "/ˈkʌltʃər/",
        "cup": "/kʌp/",
        "curious": "/ˈkjʊəriəs/",
        "current": "/ˈkɜːrənt/",
        "customer": "/ˈkʌstəmər/",
        "cut": "/kʌt/",
        "cute": "/kjuːt/",
        "cycle": "/ˈsaɪkəl/"
    }

def read_current_words():
    """读取当前的单词数据"""
    try:
        with open('words_data.js', 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"读取文件出错：{e}")
        return None

def update_words_with_phonetics():
    """更新单词数据，添加音标"""
    phonetic_dict = create_complete_phonetic_dict()
    content = read_current_words()
    
    if not content:
        return
    
    # 使用正则表达式找到所有单词条目
    pattern = r'\{id: (\d+), english: "([^"]+)", chinese: "([^"]+)", partOfSpeech: "([^"]+)"(?:, phonetic: "[^"]*")?, example: "([^"]+)"\}'
    
    def replace_word(match):
        word_id = match.group(1)
        english = match.group(2)
        chinese = match.group(3)
        part_of_speech = match.group(4)
        example = match.group(5)
        
        # 获取音标
        phonetic = phonetic_dict.get(english, "")
        
        # 构建新的条目
        return f'{{id: {word_id}, english: "{english}", chinese: "{chinese}", partOfSpeech: "{part_of_speech}", phonetic: "{phonetic}", example: "{example}"}}'
    
    # 替换所有匹配的条目
    updated_content = re.sub(pattern, replace_word, content)
    
    # 写入更新后的文件
    try:
        with open('words_data_with_phonetics.js', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print("成功生成包含完整音标的 words_data_with_phonetics.js 文件")
    except Exception as e:
        print(f"写入文件出错：{e}")

if __name__ == "__main__":
    update_words_with_phonetics()
