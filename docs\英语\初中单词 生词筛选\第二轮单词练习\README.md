# 初中单词背诵练习系统

这是一个基于Web的单词背诵练习系统，专门为初中英语单词学习设计。

## 功能特点

### 🎯 核心功能
- **分组练习**：442个单词分为5组，每组100个单词（最后一组42个）
- **智能重复**：答错的单词会重复出现，直到答对为止
- **随机出题**：每次练习都是随机顺序，避免记忆顺序
- **实时反馈**：立即显示答案正确性，提供正确答案
- **详细答案**：答错时显示音标、例句等详细信息，帮助理解和记忆

### 📊 进度跟踪
- **可视化进度条**：实时显示练习进度
- **统计信息**：显示总单词数、已完成数、错误次数
- **完成提示**：练习结束后显示统计结果

### 💾 数据管理
- **JSON导出**：导出错误单词记录为标准JSON格式
- **多文件导入**：支持同时选择多个JSON文件进行批量导入
- **智能去重**：自动识别并去除重复的单词
- **数据交换**：标准JSON格式，便于程序处理和数据共享

### 🎨 用户体验
- **护眼设计**：深色主题，柔和配色，长时间使用不疲劳
- **响应式布局**：支持手机、平板、电脑等各种设备
- **键盘操作**：支持回车键提交答案，提高操作效率
- **灵活导航**：练习中可随时返回首页，导入新的单词文件

## 使用方法

### 开始练习
1. 打开 `vocabulary_practice.html` 文件
2. 选择要练习的单词组（第1组到第5组）
3. 点击"开始练习"按钮
4. 根据显示的中文和词性，输入对应的英文单词
5. 按回车键或点击提交按钮确认答案

### 练习规则
- ✅ **答对**：单词从练习列表中移除，1秒后自动跳转下一个
- ❌ **答错**：显示正确答案和例句，需要按回车键确认后才能继续
- 🔄 **重复练习**：只有答对的单词才会被移除，确保掌握每个单词
- ⌨️ **确认机制**：答错时必须手动确认，确保用户有足够时间学习
- 🎯 **智能随机**：答错次数多的单词有更高概率出现，加强薄弱环节

### 导出错误记录
1. 练习结束后，点击"导出错误单词"按钮
2. 系统会下载一个JSON文件，包含所有答错的单词
3. 文件名格式：`wrong_words_YYYY-MM-DD.json`

### 导入错误记录
1. 点击"导入单词"按钮
2. 选择之前导出的JSON文件
3. 系统会自动加载错误单词并开始练习

## 文件结构

```
第二轮单词练习/
├── vocabulary_practice.html    # 主页面文件
├── words_data.js              # 单词数据文件（442个单词）
├── consolidated_words.txt     # 原始单词数据
├── generate_words_data.py     # 数据生成脚本
└── README.md                  # 说明文档
```

## 单词数据

### 数据来源
- 基于 `consolidated_words.txt` 文件中的442个单词
- 包含A-F开头的初中英语单词
- 每个单词包含：英文、中文释义、词性、音标、例句

### 分组情况
- **第1组**：单词1-100（a few ~ branch）
- **第2组**：单词101-200（bravo ~ competition）
- **第3组**：单词201-300（complaint ~ duty）
- **第4组**：单词301-400（each ~ flash）
- **第5组**：单词401-442（flat ~ future）

## 技术特点

### 前端技术
- **纯HTML/CSS/JavaScript**：无需额外框架，直接在浏览器中运行
- **现代CSS**：使用Flexbox布局、CSS渐变、动画效果
- **响应式设计**：适配各种屏幕尺寸

### 数据处理
- **Python脚本**：自动从文本文件生成JavaScript数据
- **正则表达式**：精确解析单词格式
- **JSON格式**：标准化的数据交换格式

### 用户体验
- **深色主题**：`#1a1a1a` 背景色，护眼设计
- **柔和配色**：蓝色系主色调，避免刺眼
- **平滑动画**：按钮悬停、进度条等都有平滑过渡效果

## 使用建议

### 学习策略
1. **循序渐进**：建议按组顺序练习，从第1组开始
2. **重复练习**：对于经常出错的单词，可以导出后单独练习
3. **定期复习**：建议每周复习之前练习过的单词组

### 最佳实践
- 每次练习时间控制在20-30分钟
- 在安静的环境中练习，提高专注度
- 答错时仔细阅读例句，理解单词用法
- 定期导出错误记录，分析薄弱环节

## 故障排除

### 常见问题
1. **页面无法加载**：确保 `words_data.js` 文件在同一目录下
2. **单词不显示**：检查浏览器控制台是否有JavaScript错误
3. **导出功能不工作**：确保浏览器支持文件下载功能

### 浏览器兼容性
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 更新日志

### v1.11.0 (2025-07-21)
- 📝 智能文件命名：导出文件名包含组号信息
- 📁 组别标识：第1-5组显示"第X组"，自定义导入显示"自定义"
- 📊 元数据增强：JSON文件包含组号和组类型信息
- 🎯 文件管理：便于用户识别和管理不同组的错误记录

### v1.10.0 (2025-07-21)
- 🚫 移除TXT导出：简化导出功能，只保留JSON格式
- 📁 专注JSON：统一使用JSON格式进行数据交换
- 🎯 功能精简：减少不必要的功能，提升用户体验
- ⚡ 性能优化：减少文件生成时间和存储空间

### v1.9.0 (2025-07-21)
- 📁 多文件导入：支持同时选择多个JSON文件进行批量导入
- 🔄 智能合并：自动合并多个文件中的单词，去除重复项
- 📊 来源追踪：记录每个单词的来源文件信息
- 🎯 批量处理：一次操作导入所有错误单词文件

### v1.8.0 (2025-07-21)
- 📄 双格式导出：错误单词同时导出JSON和TXT两种格式（已在v1.10.0中移除TXT）
- 📊 TXT格式优化：包含完整的单词信息和错误统计（已移除）
- 📁 文件命名统一：使用时间戳命名
- 🎯 用户便利：满足不同用户的文件格式需求

### v1.7.0 (2025-07-21)
- 🏠 新增返回首页按钮：练习中可随时返回首页导入新文件
- 🔄 灵活切换：支持在不同单词集合间快速切换练习
- ⚠️ 安全确认：返回首页前会提示确认，避免误操作丢失进度
- 🎯 用户友好：提升练习流程的灵活性和便利性

### v1.6.0 (2025-07-21)
- 🚫 移除音标显示：简化界面，专注单词拼写练习
- 🎯 纯净体验：答错时只显示正确单词和例句
- 🔧 代码优化：移除音标相关的显示逻辑
- 📝 数据清理：单词数据不再包含音标字段

### v1.4.1 (2025-07-21)
- 🔊 音标数据修复：为前40个单词添加完整音标信息
- 🎯 显示优化：确保答错时能正确显示音标
- 📚 数据完善：逐步扩展音标数据库

### v1.4.0 (2025-07-21)
- 🎯 智能随机算法：答错次数多的单词有更高出现概率
- 🔄 权重系统：基础权重1，每答错一次增加2的权重
- 📊 调试信息：控制台显示练习列表和错误统计
- 🧠 学习优化：重点练习薄弱单词，提高学习效率

### v1.3.2 (2025-07-21)
- 📍 布局优化：练习进度模块移至页面底部
- 🎯 视觉层次：练习内容更突出，进度信息不干扰学习
- 🎨 样式改进：进度模块增加边框和阴影，更加美观

### v1.3.1 (2025-07-21)
- 🔊 音标显示优化：正确答案中音标显示更清晰
- 🎨 界面微调：音标与单词之间增加适当间距
- 💡 条件显示：只有存在音标时才显示，避免空白

### v1.3.0 (2025-07-21)
- ⌨️ 新增确认机制：答错时需要按回车键确认后才能继续
- 💡 智能提示：答错时显示"按回车键继续下一个单词"
- 🎯 学习优化：确保用户有足够时间阅读正确答案和例句
- 🔄 状态管理：完善的确认状态控制逻辑

### v1.2.0 (2025-07-21)
- 🎛️ 简化界面：移除暂停和结束练习按钮
- 📍 重新布局：导出按钮移至选择组后面
- 🔄 优化流程：导入功能仅在首页显示
- 🎨 界面优化：更简洁的用户体验

### v1.1.0 (2025-07-21)
- 🔊 新增音标显示功能
- 📖 答错时显示详细信息（音标+例句）
- 🎨 优化错误反馈界面设计
- ⏱️ 延长错误答案显示时间至3秒

### v1.0.0 (2025-07-21)
- ✨ 初始版本发布
- 🎯 实现分组练习功能
- 📊 添加进度跟踪
- 💾 支持导入导出
- 🎨 深色护眼主题

---

**开发者**：Augment Agent
**更新时间**：2025年7月21日
**版本**：v1.11.0
