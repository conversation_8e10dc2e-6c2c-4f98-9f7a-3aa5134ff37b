#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移除所有单词数据中的音标字段
"""

import re

def remove_phonetics_from_words():
    """从单词数据中移除所有音标字段"""
    try:
        # 读取当前的单词数据文件
        with open('words_data.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式移除音标字段
        # 匹配模式：phonetic: "任何内容",
        pattern = r'phonetic: "[^"]*", '

        # 替换为空字符串，即移除音标字段
        updated_content = re.sub(pattern, '', content)
        
        # 写入更新后的文件
        with open('words_data.js', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("成功移除所有音标数据")
        
        # 统计移除的音标数量
        removed_count = len(re.findall(pattern, content))
        print(f"共移除了 {removed_count} 个音标字段")
        
    except Exception as e:
        print(f"处理文件时出错：{e}")

if __name__ == "__main__":
    remove_phonetics_from_words()
