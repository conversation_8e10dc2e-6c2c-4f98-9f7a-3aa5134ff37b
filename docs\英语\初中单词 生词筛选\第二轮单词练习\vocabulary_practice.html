<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>艾宾浩斯学习计划表 - 初中单词背诵练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #fdfaf4;
            color: #e0e0e0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            flex: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #2d3748, #4a5568);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            color: #90cdf4;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .header p {
            color: #cbd5e0;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            align-items: center;
        }

        .group-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .group-selector label {
            color: #a0aec0;
            font-weight: 500;
        }

        .group-selector select {
            background-color: #2d3748;
            color: #e2e8f0;
            border: 2px solid #4a5568;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .group-selector select:focus {
            border-color: #90cdf4;
            box-shadow: 0 0 0 3px rgba(144, 205, 244, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
        }

        .btn:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #68d391, #48bb78);
        }

        .btn.secondary:hover {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }

        .btn.danger {
            background: linear-gradient(135deg, #fc8181, #e53e3e);
        }

        .btn.danger:hover {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }

        .practice-area {
            background-color: #2d3748;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            text-align: center;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .word-info {
            margin-bottom: 30px;
        }

        .word-info .chinese {
            font-size: 2.5em;
            color: #90cdf4;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .word-info .part-of-speech {
            font-size: 1.3em;
            color: #fbb6ce;
            margin-bottom: 20px;
            font-style: italic;
        }

        .input-area {
            margin-bottom: 25px;
        }

        .input-area input {
            background-color: #1a202c;
            color: #e2e8f0;
            border: 2px solid #4a5568;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 1.5em;
            width: 100%;
            max-width: 400px;
            text-align: center;
            outline: none;
            transition: all 0.3s ease;
        }

        .input-area input:focus {
            border-color: #90cdf4;
            box-shadow: 0 0 0 3px rgba(144, 205, 244, 0.1);
        }

        .feedback {
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: 500;
            min-height: 30px;
        }

        .feedback.correct {
            color: #68d391;
        }

        .feedback.incorrect {
            color: #fc8181;
            max-width: 100%;
            overflow-wrap: break-word;
        }

        .feedback.incorrect .phonetic {
            color: #fbb6ce;
            font-style: italic;
            margin-left: 8px;
        }

        .feedback.incorrect .example-box {
            background-color: #2d3748;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
            padding: 12px;
            margin: 10px 0;
            text-align: left;
        }

        .feedback.incorrect .example-label {
            color: #a0aec0;
            font-size: 0.9em;
            margin-bottom: 5px;
            font-weight: normal;
        }

        .feedback.incorrect .example-text {
            color: #e2e8f0;
            line-height: 1.4;
            font-weight: normal;
        }

        .progress {
            background-color: #2d3748;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid #4a5568;
        }

        .progress-bar {
            background-color: #4a5568;
            height: 12px;
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            background: linear-gradient(90deg, #4299e1, #90cdf4);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 6px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #90cdf4;
        }

        .stat-label {
            color: #a0aec0;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .file-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .file-label {
            background: linear-gradient(135deg, #805ad5, #6b46c1);
            color: white;
            padding: 10px 18px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: background-color 0.2s ease;
            font-family: 'KaiTi', 'STKaiti', serif;
            border: none;
            display: inline-block;
            text-decoration: none;
        }

        .file-label:hover {
            background: linear-gradient(135deg, #6b46c1, #553c9a);
        }

        .welcome-screen {
            text-align: center;
            color: #a0aec0;
        }

        .welcome-screen h2 {
            color: #90cdf4;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .welcome-screen p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        /* 艾宾浩斯学习计划表样式 */
        .ebbinghaus-container {
            font-family: 'KaiTi', 'STKaiti', serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            background-color: #fdfaf4;
            padding: 20px;
            box-sizing: border-box;
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        .ebbinghaus-container h1 {
            color: #5a4a42;
            margin-bottom: 25px;
            font-weight: normal;
            border-bottom: 2px solid #ccbbaa;
            padding-bottom: 10px;
            text-align: center;
        }

        #selection-container {
            width: 95%;
            max-width: 1300px;
            background-color: #fff;
            border: 1px solid #ccbbaa;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 30px;
            box-sizing: border-box;
            text-align: center;
            margin-bottom: 20px;
        }

        #selection-container h2 {
            font-weight: normal;
            color: #6a5a52;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        #selection-container h3 {
            font-weight: normal;
            color: #7a6a52;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .instructions {
            margin-bottom: 20px;
            color: #666;
            line-height: 1.6;
        }

        #study-plan-setup-area {
            margin-top: 30px;
            border-top: 1px dashed #ccbbaa;
            padding-top: 20px;
        }

        #draggable-numbers-title {
            font-size: 1.1em;
            color: #444;
            margin-bottom: 10px;
            text-align: left;
        }

        #draggable-numbers-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 10px;
            border: 1px solid #e0d8c8;
            border-radius: 5px;
            background-color: #fdfaf4e0;
            margin-bottom: 20px;
            min-height: 50px;
            justify-content: flex-start;
        }

        .draggable-number {
            padding: 5px 10px;
            border: 1px solid #ccbbaa;
            border-radius: 4px;
            background-color: #fff;
            cursor: grab;
            font-family: 'KaiTi', 'STKaiti', serif;
            font-size: 0.9em;
            transition: background-color 0.2s, color 0.2s;
            user-select: none;
        }

        .draggable-number:active {
            cursor: grabbing;
        }

        /* 移除used状态样式，因为允许多次拖拽 */

        .draggable-number.clickable {
            cursor: pointer;
            background-color: #e6f7ff;
            border-color: #4299e1;
        }

        .draggable-number.clickable:hover {
            background-color: #bae7ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .word-info .chinese {
                font-size: 2em;
            }

            .input-area input {
                font-size: 1.2em;
            }

            .stats {
                flex-direction: column;
                gap: 15px;
            }

            .ebbinghaus-container {
                padding: 15px;
                font-size: 14px;
            }

            .ebbinghaus-container h1 {
                font-size: 1.5em;
            }

            #selection-container {
                width: 100%;
                padding: 15px;
            }

            .plan-row-buttons {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }

            #add-plan-row-button, #remove-plan-row-button {
                width: 80%;
                max-width: 200px;
            }

            /* 移动端表格样式 */
            #study-plan-table {
                font-size: 0.75em;
            }

            #study-plan-table th, #study-plan-table td {
                padding: 3px;
                min-width: 30px;
            }

            /* 移动端折叠列 */
            #study-plan-table th:nth-child(1),
            #study-plan-table td:nth-child(1) {
                min-width: 30px;
                width: 30px;
                max-width: 30px;
            }

            /* 移动端序号列 - 能显示三位数字 */
            #study-plan-table th:nth-child(2),
            #study-plan-table td:nth-child(2) {
                min-width: 40px;
                width: 40px;
                max-width: 40px;
            }

            /* 移动端学习日期列 - 能显示六位数字 */
            #study-plan-table th:nth-child(3),
            #study-plan-table td:nth-child(3) {
                min-width: 65px;
                width: 65px;
                max-width: 65px;
            }

            /* 移动端学习内容列 */
            #study-plan-table th:nth-child(4),
            #study-plan-table td:nth-child(4) {
                min-width: 120px;
                width: auto;
                max-width: none;
            }

            /* 移动端复习列 */
            #study-plan-table th:nth-child(n+5),
            #study-plan-table td:nth-child(n+5) {
                min-width: 50px;
                width: 50px;
                max-width: 50px;
            }

            .review-cell-content input[type="text"] {
                width: 95%;
                font-size: 0.8em;
                padding: 2px;
            }

            .review-cell-content button {
                font-size: 0.75em;
                padding: 1px 4px;
                margin-top: 1px;
            }
        }

        /* 表格样式 */
        #study-plan-table-container {
            overflow-x: auto;
        }

        #study-plan-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 0.85em;
        }

        #study-plan-table th, #study-plan-table td {
            border: 1px solid #ccbbaa;
            padding: 6px;
            text-align: center;
            min-width: 55px;
            vertical-align: top;
        }

        /* 折叠功能样式 */
        .collapse-toggle {
            background-color: #f0f0f0;
            border: 1px solid #ccbbaa;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 0.8em;
            color: #666;
            user-select: none;
            transition: background-color 0.2s;
            min-width: 30px;
            border-radius: 3px;
        }

        .collapse-toggle:hover {
            background-color: #e0e0e0;
        }

        .collapse-toggle.collapsed {
            background-color: #d0d0d0;
        }

        .row-group-collapsed {
            display: none;
        }

        .group-header-row {
            background-color: #f7f3e9;
            font-weight: bold;
        }

        .group-header-row td {
            background-color: #f7f3e9;
            border-bottom: 2px solid #ccbbaa;
        }

        /* 折叠列 */
        #study-plan-table th:nth-child(1),
        #study-plan-table td:nth-child(1) {
            min-width: 40px;
            width: 40px;
            max-width: 40px;
        }

        /* 序号列 - 能显示三位数字 */
        #study-plan-table th:nth-child(2),
        #study-plan-table td:nth-child(2) {
            min-width: 45px;
            width: 45px;
            max-width: 45px;
        }

        /* 学习日期列 - 能显示六位数字 */
        #study-plan-table th:nth-child(3),
        #study-plan-table td:nth-child(3) {
            min-width: 75px;
            width: 75px;
            max-width: 75px;
        }

        /* 学习内容列占用剩余空间 */
        #study-plan-table th:nth-child(4),
        #study-plan-table td:nth-child(4) {
            min-width: 200px;
            width: auto;
            max-width: none;
        }

        /* 复习列设置固定宽度 */
        #study-plan-table th:nth-child(n+5),
        #study-plan-table td:nth-child(n+5) {
            min-width: 70px;
            width: 70px;
            max-width: 70px;
        }

        #study-plan-table th {
            background-color: #f7f3e9;
            color: #6a5a52;
            font-weight: normal;
            white-space: nowrap;
        }

        .study-date-input {
            width: 90%;
            padding: 4px;
            font-size: 0.95em;
            border: 1px solid #ccc;
            border-radius: 3px;
            text-align: center;
            box-sizing: border-box;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        .study-content-cell {
            background-color: #fff;
            min-width: 120px;
            min-height: 40px;
            vertical-align: top;
            text-align: left;
            padding: 5px;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            align-content: flex-start;
            gap: 3px;
            position: relative;
        }

        .study-content-cell.drag-over {
            background-color: #e6f7ff;
            border-style: dashed;
        }

        .dropped-item-tag {
            display: inline-flex;
            align-items: center;
            background-color: #d1e7fd;
            color: #084298;
            padding: 3px 6px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.95em;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .dropped-item-tag:hover {
            background-color: #4299e1;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
        }

        .remove-item-btn {
            margin-left: 5px;
            color: #ae2a2a;
            cursor: pointer;
            font-weight: bold;
            padding: 0 3px;
            border-radius: 50%;
        }

        .remove-item-btn:hover {
            background-color: #f8d7da;
        }

        .review-cell-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .review-cell-content input[type="text"] {
            width: 90%;
            padding: 3px;
            font-size: 0.85em;
            border: 1px solid #ccc;
            border-radius: 3px;
            text-align: center;
            box-sizing: border-box;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        .review-cell-content button {
            padding: 2px 6px;
            font-size: 0.8em;
            background-color: #8fbc8f;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-family: 'KaiTi', 'STKaiti', serif;
            margin-top: 2px;
        }

        .review-cell-content button:hover {
            background-color: #70a070;
        }

        .review-cell-content button:disabled {
            background-color: #ccc;
            color: #666;
            cursor: not-allowed;
        }

        .review-cell-content input[type="text"]:read-only {
            background-color: #f0f0f0;
            border-color: #ddd;
        }

        /* 按钮样式 */
        .plan-row-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }

        #add-plan-row-button, #remove-plan-row-button {
            padding: 8px 15px;
            color: #333;
            border: 1px solid;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'KaiTi', 'STKaiti', serif;
            transition: background-color 0.2s ease;
        }

        #add-plan-row-button {
            background-color: #90ee90;
            border-color: #7cb37c;
        }

        #add-plan-row-button:hover {
            background-color: #7ccd7c;
        }

        #remove-plan-row-button {
            background-color: #ffb3b3;
            border-color: #ff8080;
        }

        #remove-plan-row-button:hover {
            background-color: #ff9999;
        }

        #remove-plan-row-button:disabled {
            background-color: #e0e0e0;
            border-color: #ccc;
            color: #888;
            cursor: not-allowed;
        }

        .plan-action-buttons-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        #save-plan-button, #reset-plan-button, #print-button, #undo-button, #goto-practice-button {
            padding: 10px 18px;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        #save-plan-button {
            background-color: #7e8a5f;
        }

        #reset-plan-button {
            background-color: #c86c5d;
        }

        #print-button {
            background-color: #28a745;
        }

        #undo-button {
            background-color: #6c757d;
        }

        .goto-practice-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
        }

        #save-plan-button:hover {
            background-color: #6a734f;
        }

        #reset-plan-button:hover {
            background-color: #b05040;
        }

        #print-button:hover {
            background-color: #1e7e34;
        }

        #undo-button:hover {
            background-color: #545b62;
        }

        .goto-practice-btn:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
        }

        #undo-button:disabled {
            background-color: #adb5bd;
            cursor: not-allowed;
        }

        /* 打印样式 */
        @media print {
            .ebbinghaus-container {
                background: white;
                padding: 0;
            }

            /* 隐藏不需要打印的元素 */
            .plan-action-buttons-container,
            #add-plan-row-button,
            #draggable-numbers-title,
            #draggable-numbers-container {
                display: none;
            }

            #selection-container h2,
            .instructions {
                display: none;
            }

            /* 保持表格容器显示 */
            #study-plan-setup-area {
                border-top: none;
                padding-top: 0;
                margin-top: 0;
            }

            #study-plan-table-container {
                margin-top: 0;
            }

            #study-plan-table-container h3 {
                display: none;
            }

            #study-plan-table {
                font-size: 11px;
                margin-top: 0;
                width: 100%;
            }

            #study-plan-table th,
            #study-plan-table td {
                padding: 3px 2px;
                font-size: 10px;
                border: 1px solid #000;
            }

            #study-plan-table th {
                background-color: #f0f0f0 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .ebbinghaus-container h1 {
                margin-bottom: 15px;
                font-size: 1.6em;
                text-align: center;
            }

            #selection-container {
                padding: 10px;
                margin-bottom: 0;
                border: none;
                box-shadow: none;
            }

            /* 确保拖拽的内容在打印时可见 */
            .dropped-item-tag {
                background-color: #e0e0e0 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                border: 1px solid #999;
            }

            .remove-item-btn {
                display: none;
            }

            /* 隐藏复习单元格中的"完成"按钮 */
            .review-cell-content button {
                display: none;
            }

            /* 调整复习单元格布局，去掉按钮后只显示输入框 */
            .review-cell-content {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 艾宾浩斯学习计划表首页 -->
    <div id="ebbinghaus-homepage" class="ebbinghaus-container">
        <h1>艾宾浩斯学习计划表</h1>

        <div id="selection-container">
            <h2>设置学习计划</h2>
            <p class="instructions">将下方的学习单元拖拽到计划表的"学习内容"栏中进行规划。<br>在各复习时间点手动输入复习的单元编号，并点击"完成"标记。<br><strong>💡 提示：每个格子可拖入多个学习单元，学习单元可重复使用，点击可直接进入对应的单词练习！每组包含100个单词（第14组50个）</strong></p>

            <div id="study-plan-setup-area">
                <div id="draggable-numbers-title">可拖拽的学习单元 (第1-14组，每组100个单词) - 点击可直接练习:</div>
                <div id="draggable-numbers-container"></div>

                <div id="study-plan-table-container">
                    <h3>艾宾浩斯遗忘曲线复习计划表</h3>
                    <table id="study-plan-table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">折叠</th>
                                <th>序号</th>
                                <th>学习日期</th>
                                <th>学习内容 (拖入单元)</th>
                                <th>第1天</th>
                                <th>第2天</th>
                                <th>第3天</th>
                                <th>第5天</th>
                                <th>第8天</th>
                                <th>第16天</th>
                                <th>第31天</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>

                <div class="plan-row-buttons">
                    <button id="add-plan-row-button">添加新学习日</button>
                    <button id="remove-plan-row-button">删减学习日</button>
                </div>

                <div class="plan-action-buttons-container">
                    <input type="file" id="importFile" class="file-input" accept=".json" multiple onchange="importWords()">
                    <label for="importFile" class="file-label">📁 导入单词（可选择多个文件）</label>
                    <button id="print-button">🖨️ 打印学习表</button>
                    <button id="save-plan-button">💾 保存学习计划</button>
                    <button id="undo-button">↶ 撤销操作</button>
                    <button id="reset-plan-button">🗑️ 重置学习计划</button>
                    <button id="goto-practice-button" class="goto-practice-btn">📚 进入单词练习</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 词汇练习界面 -->
    <div id="vocabulary-practice" class="container" style="display: none;">
        <div class="header">
            <h1>初中单词背诵练习</h1>
            <p>第二轮单词练习 - 共1350个单词</p>
        </div>

        <div class="controls">
            <div class="group-selector">
                <label for="groupSelect">选择练习组:</label>
                <select id="groupSelect">
                    <option value="">请选择...</option>
                </select>
            </div>
            <button class="btn" id="startBtn" onclick="startPractice()">开始练习</button>
            <button class="btn secondary" id="exportBtn" onclick="exportWrongWords()" style="display: none;">导出错误单词</button>
            <button class="btn secondary" id="backBtn" onclick="backToHome()" style="display: none;">返回首页</button>
        </div>



        <div class="practice-area" id="practiceArea">
            <div class="welcome-screen">
                <h2>欢迎使用单词背诵练习</h2>
                <p>📚 选择一个练习组开始背单词</p>
                <p>💡 每组包含100个单词（第14组50个）</p>
                <p>✨ 答错的单词会重复出现，直到答对为止</p>
                <p>📊 练习结束后可以导出错误单词记录</p>
            </div>
        </div>

        <div class="progress" id="progressArea" style="display: none;">
            <div>练习进度</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalWords">0</div>
                    <div class="stat-label">总单词数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedWords">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="wrongWords">0</div>
                    <div class="stat-label">错误次数</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入单词数据 -->
    <script src="words_data.js"></script>

    <script>
        // 页面状态管理
        let currentPage = 'ebbinghaus'; // 'ebbinghaus' 或 'practice'

        // 检查单词数据是否加载
        console.log('WORDS_DATA 是否定义:', typeof WORDS_DATA !== 'undefined');
        if (typeof WORDS_DATA !== 'undefined') {
            console.log('WORDS_DATA 长度:', WORDS_DATA.length);
            console.log('第一个单词:', WORDS_DATA[0]);
        } else {
            console.error('WORDS_DATA 未定义！');
        }

        // 艾宾浩斯计划表相关变量
        let draggableNumbersState = {};
        const planTableStorageKey = 'ebbinghausStudyPlan2025_v1';
        const collapseStateStorageKey = 'ebbinghausCollapseState2025_v1';
        let historyStack = [];
        const maxHistorySize = 20;

        // 折叠状态管理
        let collapseStates = {}; // 存储每个组的折叠状态
        const rowsPerGroup = 10; // 每组10行

        // 生成空白的初始计划表结构（不自动填充日期）
        let initialPlanTableStructure = [];
        let seq = 1;

        // 创建14行空白的学习计划（1350个单词分为14组）
        for (let i = 0; i < 14; i++) {
            initialPlanTableStructure.push({ seq: seq++, date: "" });
        }

        console.log('初始计划表结构:', initialPlanTableStructure); // 调试信息

        const reviewIntervalHeaders = ["第1天", "第2天", "第3天", "第5天", "第8天", "第16天", "第31天"];

        // 练习状态
        let currentGroup = [];
        let practiceWords = [];
        let currentWordIndex = 0;
        let currentWord = null;
        let isPracticing = false;
        let wrongWordsCount = {};
        let completedWordsSet = new Set();
        let isWaitingForConfirm = false; // 是否等待用户确认
        let currentGroupNumber = 0; // 当前练习组号
        
        // 页面切换功能
        function showEbbinghausPage() {
            document.getElementById('ebbinghaus-homepage').style.display = 'block';
            document.getElementById('vocabulary-practice').style.display = 'none';
            document.body.style.backgroundColor = '#fdfaf4'; // 艾宾浩斯页面背景
            currentPage = 'ebbinghaus';
        }

        function showPracticePage() {
            document.getElementById('ebbinghaus-homepage').style.display = 'none';
            document.getElementById('vocabulary-practice').style.display = 'block';
            document.body.style.backgroundColor = '#1a1a1a';
            currentPage = 'practice';
        }

        // 从学习组启动练习
        function startPracticeFromGroup(groupNumber) {
            // 切换到练习页面
            showPracticePage();

            // 设置选择的组
            const groupSelect = document.getElementById('groupSelect');
            if (groupSelect) {
                groupSelect.value = groupNumber;

                // 自动开始练习
                setTimeout(() => {
                    startPractice();
                }, 100);
            }
        }

        // 艾宾浩斯计划表功能
        function saveToHistory() {
            const currentState = getCurrentPlanState();
            historyStack.push(JSON.parse(JSON.stringify(currentState)));

            if (historyStack.length > maxHistorySize) {
                historyStack.shift();
            }

            updateUndoButtonState();
        }

        function getCurrentPlanState() {
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            const planState = {
                tableData: [],
                draggableNumbersState: { ...draggableNumbersState }
            };

            studyPlanTableBody.querySelectorAll('tr').forEach(tr => {
                const rowIndex = parseInt(tr.dataset.rowIndex, 10);
                const rowData = {
                    seq: parseInt(tr.cells[1].textContent, 10), // 序号现在在第1列
                    date: tr.cells[2].querySelector('input.study-date-input').value, // 日期现在在第2列
                    studyContent: [],
                    reviewCells: []
                };

                tr.cells[3].querySelectorAll('.dropped-item-tag').forEach(tag => { // 学习内容现在在第3列
                    rowData.studyContent.push(tag.dataset.value);
                });

                tr.querySelectorAll('.review-cell-content').forEach((rcDiv) => {
                    const input = rcDiv.querySelector('input[type="text"]');
                    const button = rcDiv.querySelector('button');
                    rowData.reviewCells.push({
                        text: input.value,
                        done: button.disabled
                    });
                });

                planState.tableData[rowIndex] = rowData;
            });

            planState.tableData = planState.tableData.filter(Boolean);
            return planState;
        }

        function restoreFromState(state) {
            draggableNumbersState = { ...state.draggableNumbersState };
            generateDraggableNumbers();

            const tableDataForRendering = state.tableData.length > 0
                                         ? state.tableData
                                         : JSON.parse(JSON.stringify(initialPlanTableStructure));
            generateStudyPlanTable(tableDataForRendering, state);
        }

        function undoLastOperation() {
            if (historyStack.length > 0) {
                const previousState = historyStack.pop();
                restoreFromState(previousState);
                updateUndoButtonState();
            }
        }

        function updateUndoButtonState() {
            const undoButton = document.getElementById('undo-button');
            if (undoButton) {
                undoButton.disabled = historyStack.length === 0;
            }
        }

        // 初始化页面
        function initializePage() {
            populateGroupSelector();
            setupEventListeners();
            initializeEbbinghausPage();
        }
        
        // 填充组选择器
        function populateGroupSelector() {
            const select = document.getElementById('groupSelect');
            const totalGroups = Math.ceil(WORDS_DATA.length / 100);
            
            for (let i = 1; i <= totalGroups; i++) {
                const option = document.createElement('option');
                const startIndex = (i - 1) * 100;
                const endIndex = Math.min(i * 100, WORDS_DATA.length);
                const count = endIndex - startIndex;
                
                option.value = i;
                option.textContent = `第${i}组 (${startIndex + 1}-${endIndex}, 共${count}个单词)`;
                select.appendChild(option);
            }
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            const input = document.getElementById('answerInput');
            if (input) {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        checkAnswer();
                    }
                });
            }
        }
        
        // 开始练习
        function startPractice() {
            const groupSelect = document.getElementById('groupSelect');
            const selectedGroup = parseInt(groupSelect.value);

            if (!selectedGroup) {
                alert('请先选择一个练习组！');
                return;
            }

            // 获取选中组的单词
            const startIndex = (selectedGroup - 1) * 100;
            const endIndex = Math.min(selectedGroup * 100, WORDS_DATA.length);
            currentGroup = WORDS_DATA.slice(startIndex, endIndex);
            currentGroupNumber = selectedGroup; // 记录当前组号

            // 初始化练习数据
            practiceWords = [...currentGroup];
            currentWordIndex = 0;
            wrongWordsCount = {};
            completedWordsSet = new Set();
            isPracticing = true;
            isWaitingForConfirm = false;

            // 更新UI
            document.getElementById('startBtn').style.display = 'none';
            document.getElementById('exportBtn').style.display = 'inline-block';
            document.getElementById('backBtn').style.display = 'inline-block';
            document.getElementById('progressArea').style.display = 'block';

            // 开始第一个单词
            showNextWord();
            updateProgress();
        }
        
        // 重置练习状态
        function resetPractice() {
            isPracticing = false;
            currentGroup = [];
            practiceWords = [];
            currentWordIndex = 0;
            currentWord = null;
            isWaitingForConfirm = false;
            currentGroupNumber = 0;

            // 重置UI
            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('exportBtn').style.display = 'none';
            document.getElementById('backBtn').style.display = 'none';
            document.getElementById('progressArea').style.display = 'none';

            // 显示欢迎界面（如果在练习页面）
            if (currentPage === 'practice') {
                showWelcomeScreen();
            }
        }

        // 返回首页
        function backToHome() {
            if (confirm('确定要返回首页吗？当前练习进度将会丢失。')) {
                resetPractice();
                showEbbinghausPage();
            }
        }
        
        // 显示下一个单词
        function showNextWord() {
            if (!isPracticing || practiceWords.length === 0) {
                showCompletionScreen();
                return;
            }

            // 智能随机选择单词（答错次数多的单词有更高概率出现）
            currentWordIndex = selectNextWordIndex();
            currentWord = practiceWords[currentWordIndex];
            showCurrentWord();
        }

        // 智能选择下一个单词的索引
        function selectNextWordIndex() {
            // 如果只有一个单词，直接返回
            if (practiceWords.length === 1) {
                return 0;
            }

            // 创建权重数组，答错次数多的单词权重更高
            const weights = practiceWords.map(word => {
                const errorCount = wrongWordsCount[word.id] || 0;
                // 基础权重为1，每答错一次增加2的权重
                return 1 + (errorCount * 2);
            });

            // 计算总权重
            const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

            // 生成随机数
            let random = Math.random() * totalWeight;

            // 根据权重选择单词
            for (let i = 0; i < weights.length; i++) {
                random -= weights[i];
                if (random <= 0) {
                    return i;
                }
            }

            // 兜底：返回最后一个索引
            return weights.length - 1;
        }
        
        // 显示当前单词
        function showCurrentWord() {
            if (!currentWord) return;

            // 重置等待确认状态
            isWaitingForConfirm = false;

            const practiceArea = document.getElementById('practiceArea');
            practiceArea.innerHTML = `
                <div class="word-info">
                    <div class="chinese">${currentWord.meaning}</div>
                    <div class="part-of-speech">${currentWord.pos}</div>
                </div>
                <div class="input-area">
                    <input type="text" id="answerInput" placeholder="请输入英文单词..." autocomplete="off">
                </div>
                <div class="feedback" id="feedback"></div>
            `;

            // 重新设置事件监听器
            setupEventListeners();

            // 聚焦输入框并重置状态
            setTimeout(() => {
                const answerInput = document.getElementById('answerInput');
                answerInput.focus();
                // 重置输入框状态（清除之前的错误样式）
                answerInput.disabled = false;
                answerInput.style.backgroundColor = '';
                answerInput.style.color = '';
                answerInput.style.textDecoration = '';
            }, 100);
        }
        
        // 检查答案
        function checkAnswer() {
            // 如果正在等待确认，则跳转到下一个单词
            if (isWaitingForConfirm) {
                showNextWord();
                updateProgress();
                isWaitingForConfirm = false;
                return;
            }

            const input = document.getElementById('answerInput');
            const feedback = document.getElementById('feedback');
            const userAnswer = input.value.trim().toLowerCase();
            const correctAnswer = currentWord.word.toLowerCase();

            if (userAnswer === correctAnswer) {
                // 答对了
                feedback.textContent = '✅ 正确！';
                feedback.className = 'feedback correct';

                // 从练习列表中移除这个单词
                practiceWords.splice(currentWordIndex, 1);
                completedWordsSet.add(currentWord.id);

                // 1秒后显示下一个单词
                setTimeout(() => {
                    showNextWord();
                    updateProgress();
                }, 1000);

            } else {
                // 答错了 - 显示详细的正确答案信息，保留用户输入用于对比
                feedback.innerHTML = `
                    <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                        <div style="margin-bottom: 10px;">
                            ❌ <strong style="color: #fc8181;">错误！</strong>
                        </div>
                        <div style="margin-bottom: 15px; font-size: 1.1em;">
                            <strong style="color: #fc8181;">您的答案：</strong>
                            <span style="color: #fc8181; font-size: 1.2em; text-decoration: line-through;">${userAnswer}</span>
                        </div>
                        <div style="margin-bottom: 15px; font-size: 1.1em;">
                            <strong style="color: #90cdf4;">正确答案：</strong>
                            <span style="color: #68d391; font-size: 1.2em; font-weight: bold;">${currentWord.word}</span>
                        </div>
                        <div style="margin-bottom: 10px; padding: 10px; background-color: #2d3748; border-radius: 8px; border-left: 4px solid #4299e1;">
                            <div style="color: #a0aec0; font-size: 0.9em; margin-bottom: 5px;">例句：</div>
                            <div style="color: #e2e8f0; line-height: 1.4;">${currentWord.example}</div>
                        </div>
                        <div style="margin-top: 15px; color: #a0aec0; font-size: 0.9em; text-align: center;">
                            <strong>按回车键继续下一个单词</strong>
                        </div>
                    </div>
                `;
                feedback.className = 'feedback incorrect';

                // 记录错误次数
                if (!wrongWordsCount[currentWord.id]) {
                    wrongWordsCount[currentWord.id] = 0;
                }
                wrongWordsCount[currentWord.id]++;

                // 保留用户输入，但禁用输入框并设置等待确认状态
                input.disabled = true;
                input.style.backgroundColor = '#2d3748';
                input.style.color = '#fc8181';
                input.style.textDecoration = 'line-through';
                input.placeholder = '按回车键继续...';
                isWaitingForConfirm = true;
            }
        }
        
        // 更新进度
        function updateProgress() {
            const totalWords = currentGroup.length;
            const completed = completedWordsSet.size;
            const wrong = Object.values(wrongWordsCount).reduce((sum, count) => sum + count, 0);
            const remaining = practiceWords.length;

            document.getElementById('totalWords').textContent = totalWords;
            document.getElementById('completedWords').textContent = completed;
            document.getElementById('wrongWords').textContent = wrong;

            const progress = (completed / totalWords) * 100;
            document.getElementById('progressFill').style.width = progress + '%';

            // 在控制台输出调试信息（可选）
            if (isPracticing && practiceWords.length > 0) {
                console.log(`练习进度: 总计${totalWords}, 完成${completed}, 剩余${remaining}, 错误${wrong}次`);
                console.log('当前练习列表:', practiceWords.map(w => `${w.english}(错误${wrongWordsCount[w.id] || 0}次)`));
            }
        }
        

        // 显示完成界面
        function showCompletionScreen() {
            const practiceArea = document.getElementById('practiceArea');
            const totalWords = currentGroup.length;
            const completedWords = completedWordsSet.size;
            const wrongCount = Object.keys(wrongWordsCount).length;
            const remainingWords = practiceWords.length;

            let statusMessage = '';
            if (remainingWords === 0) {
                statusMessage = '<p>🎉 全部单词都已掌握！</p>';
            } else {
                statusMessage = `<p>⚠️ 还有 ${remainingWords} 个单词需要继续练习</p>`;
            }

            practiceArea.innerHTML = `
                <div class="welcome-screen">
                    <h2>🎉 练习完成！</h2>
                    <p>总共练习了 ${totalWords} 个单词</p>
                    <p>已掌握 ${completedWords} 个单词</p>
                    <p>答错的单词有 ${wrongCount} 个</p>
                    ${statusMessage}
                    ${wrongCount > 0 ? '<p>可以点击"导出错误单词"按钮保存错误记录</p>' : ''}
                </div>
            `;

            // 重置按钮状态
            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('backBtn').style.display = 'inline-block';
            isPracticing = false;
        }
        
        // 显示欢迎界面
        function showWelcomeScreen() {
            const practiceArea = document.getElementById('practiceArea');
            if (practiceArea) {
                practiceArea.innerHTML = `
                    <div class="welcome-screen">
                        <h2>欢迎使用单词背诵练习</h2>
                        <p>📚 选择一个练习组开始背单词</p>
                        <p>💡 每组包含100个单词（最后一组50个）</p>
                        <p>✨ 答错的单词会重复出现，直到答对为止</p>
                        <p>📊 练习结束后可以导出错误单词记录</p>
                    </div>
                `;
            }
        }
        
        // 导出错误单词
        function exportWrongWords() {
            if (Object.keys(wrongWordsCount).length === 0) {
                alert('没有错误单词需要导出！');
                return;
            }

            const wrongWords = [];
            for (const [wordId, count] of Object.entries(wrongWordsCount)) {
                const word = WORDS_DATA.find(w => w.id == wordId);
                if (word) {
                    wrongWords.push({
                        ...word,
                        errorCount: count
                    });
                }
            }

            const timestamp = new Date().toISOString().split('T')[0];

            // 生成文件名
            let fileName;
            if (currentGroupNumber > 0) {
                fileName = `wrong_words_第${currentGroupNumber}组_${timestamp}.json`;
            } else {
                fileName = `wrong_words_自定义_${timestamp}.json`;
            }

            // 导出JSON格式
            const exportData = {
                exportTime: new Date().toISOString(),
                groupNumber: currentGroupNumber,
                groupType: currentGroupNumber > 0 ? `第${currentGroupNumber}组` : '自定义导入',
                totalWords: currentGroup.length,
                wrongWordsCount: wrongWords.length,
                words: wrongWords
            };

            const jsonBlob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const jsonUrl = URL.createObjectURL(jsonBlob);
            const jsonLink = document.createElement('a');
            jsonLink.href = jsonUrl;
            jsonLink.download = fileName;
            document.body.appendChild(jsonLink);
            jsonLink.click();
            document.body.removeChild(jsonLink);
            URL.revokeObjectURL(jsonUrl);

            alert(`错误单词记录已导出！\n已生成JSON格式文件。`);
        }
        
        // 导入单词
        function importWords() {
            const files = document.getElementById('importFile').files;
            if (!files || files.length === 0) return;

            let allWords = [];
            let processedFiles = 0;
            let totalFiles = files.length;
            let fileNames = [];

            // 处理每个文件
            Array.from(files).forEach((file, fileIndex) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const importData = JSON.parse(e.target.result);

                        // 验证数据格式
                        if (!importData.words || !Array.isArray(importData.words)) {
                            console.warn(`文件 ${file.name} 格式错误：缺少words数组`);
                            processedFiles++;
                            checkAllFilesProcessed();
                            return;
                        }

                        // 验证单词格式
                        const validWords = importData.words.filter(word =>
                            word.english && word.chinese && word.partOfSpeech && word.example
                        );

                        if (validWords.length > 0) {
                            // 为导入的单词分配新的ID，避免重复
                            validWords.forEach((word, index) => {
                                word.id = (fileIndex + 1) * 1000 + index; // 使用文件索引避免ID冲突
                                word.sourceFile = file.name; // 记录来源文件
                            });

                            allWords = allWords.concat(validWords);
                            fileNames.push(file.name);
                        }

                        processedFiles++;
                        checkAllFilesProcessed();

                    } catch (error) {
                        console.error(`文件 ${file.name} 解析失败:`, error);
                        processedFiles++;
                        checkAllFilesProcessed();
                    }
                };

                reader.readAsText(file);
            });

            function checkAllFilesProcessed() {
                if (processedFiles === totalFiles) {
                    if (allWords.length === 0) {
                        alert('所有文件中都没有有效的单词数据');
                        return;
                    }

                    // 去重处理（基于英文单词）
                    const uniqueWords = [];
                    const seenWords = new Set();

                    allWords.forEach(word => {
                        const key = word.english.toLowerCase();
                        if (!seenWords.has(key)) {
                            seenWords.add(key);
                            uniqueWords.push(word);
                        }
                    });

                    // 创建自定义练习组
                    currentGroup = uniqueWords;
                    practiceWords = [...currentGroup];
                    currentWordIndex = 0;
                    wrongWordsCount = {};
                    completedWordsSet = new Set();
                    isPracticing = true;
                    isWaitingForConfirm = false;
                    currentGroupNumber = 0; // 自定义导入设置为0

                    // 切换到练习页面并更新UI
                    showPracticePage();
                    document.getElementById('startBtn').style.display = 'none';
                    document.getElementById('exportBtn').style.display = 'inline-block';
                    document.getElementById('backBtn').style.display = 'inline-block';
                    document.getElementById('progressArea').style.display = 'block';

                    // 开始练习
                    showNextWord();
                    updateProgress();

                    const duplicateCount = allWords.length - uniqueWords.length;
                    let message = `成功导入 ${uniqueWords.length} 个单词！\n`;
                    message += `来源文件: ${fileNames.join(', ')}\n`;
                    if (duplicateCount > 0) {
                        message += `已自动去除 ${duplicateCount} 个重复单词。`;
                    }

                    alert(message);
                }
            }
        }
        
        // 艾宾浩斯计划表初始化
        function initializeEbbinghausPage() {
            const draggableNumbersContainer = document.getElementById('draggable-numbers-container');
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            const addPlanRowButton = document.getElementById('add-plan-row-button');
            const removePlanRowButton = document.getElementById('remove-plan-row-button');
            const savePlanButton = document.getElementById('save-plan-button');
            const resetPlanButton = document.getElementById('reset-plan-button');
            const printButton = document.getElementById('print-button');
            const undoButton = document.getElementById('undo-button');
            const gotoPracticeButton = document.getElementById('goto-practice-button');

            // 事件监听器
            if (addPlanRowButton) addPlanRowButton.addEventListener('click', addStudyPlanRow);
            if (removePlanRowButton) removePlanRowButton.addEventListener('click', removeStudyPlanRow);
            if (savePlanButton) {
                console.log('保存按钮找到了，正在绑定事件监听器...');
                savePlanButton.addEventListener('click', savePlanState);
            } else {
                console.error('找不到保存按钮！');
            }
            if (resetPlanButton) resetPlanButton.addEventListener('click', resetPlanUI);
            if (printButton) printButton.addEventListener('click', printPlan);
            if (undoButton) undoButton.addEventListener('click', undoLastOperation);
            if (gotoPracticeButton) gotoPracticeButton.addEventListener('click', () => showPracticePage());

            // 加载计划状态
            loadCollapseStates(); // 先加载折叠状态
            loadPlanState();
            updateUndoButtonState();
            updatePlanRowButtons();

            // 检查是否有用户保存的数据，如果没有则保持空白状态
            setTimeout(() => {
                const savedDataString = localStorage.getItem(planTableStorageKey);
                if (!savedDataString) {
                    // 只自动填充第1天复习内容，不填充学习内容
                    autoFillFirstDayReview();
                    setTimeout(() => {
                        saveToHistory();
                    }, 50);
                }
            }, 100);
        }

        // 生成学习单元：第1组, 第2组, ..., 第14组 (对应单词练习的组别)
        function generateDraggableNumbers() {
            const draggableNumbersContainer = document.getElementById('draggable-numbers-container');
            if (!draggableNumbersContainer) return;

            // 计算总组数（基于WORDS_DATA的长度）
            const totalGroups = typeof WORDS_DATA !== 'undefined' ? Math.ceil(WORDS_DATA.length / 100) : 5;

            draggableNumbersContainer.innerHTML = '';
            for (let i = 1; i <= totalGroups; i++) {
                const unitName = `第${i}组`;

                const button = document.createElement('button');
                button.classList.add('draggable-number', 'clickable');
                button.textContent = unitName;
                button.draggable = true;
                button.dataset.number = unitName;
                button.dataset.groupNumber = i; // 存储组号

                // 添加点击功能，可以直接启动练习
                button.addEventListener('click', () => {
                    startPracticeFromGroup(i);
                });

                // 拖拽功能，始终可用
                button.addEventListener('dragstart', (e) => {
                    console.log('开始拖拽:', unitName); // 调试信息
                    e.dataTransfer.setData('text/plain', unitName);
                    e.dataTransfer.effectAllowed = 'copy'; // 改为copy模式，表示可以多次使用
                });

                draggableNumbersContainer.appendChild(button);
            }
        }

        function updateDraggableButtonState(number, used) {
            // 不再需要更新拖拽按钮状态，因为允许多次拖拽
            // 保留此函数以防其他地方调用，但不执行任何操作
            return;
        }

        // 折叠功能相关函数
        function getGroupIndex(rowIndex) {
            return Math.floor(rowIndex / rowsPerGroup);
        }

        function isGroupHeader(rowIndex) {
            return rowIndex % rowsPerGroup === 0;
        }

        function toggleGroupCollapse(groupIndex) {
            const isCollapsed = collapseStates[groupIndex] || false;
            collapseStates[groupIndex] = !isCollapsed;

            // 更新UI
            updateGroupVisibility(groupIndex);
            updateCollapseButton(groupIndex);

            // 保存折叠状态
            saveCollapseStates();
        }

        function updateGroupVisibility(groupIndex) {
            const isCollapsed = collapseStates[groupIndex] || false;
            const startRow = groupIndex * rowsPerGroup;
            const endRow = startRow + rowsPerGroup - 1;

            for (let i = startRow; i <= endRow; i++) {
                const row = document.querySelector(`#study-plan-table tbody tr[data-row-index="${i}"]`);
                if (row && !isGroupHeader(i)) {
                    if (isCollapsed) {
                        row.classList.add('row-group-collapsed');
                    } else {
                        row.classList.remove('row-group-collapsed');
                    }
                }
            }
        }

        function updateCollapseButton(groupIndex) {
            const isCollapsed = collapseStates[groupIndex] || false;
            const headerRowIndex = groupIndex * rowsPerGroup;
            const headerRow = document.querySelector(`#study-plan-table tbody tr[data-row-index="${headerRowIndex}"]`);

            if (headerRow) {
                const collapseButton = headerRow.querySelector('.collapse-toggle');
                if (collapseButton) {
                    collapseButton.textContent = isCollapsed ? '+' : '-';
                    collapseButton.title = isCollapsed ? '展开此组' : '折叠此组';
                    if (isCollapsed) {
                        collapseButton.classList.add('collapsed');
                    } else {
                        collapseButton.classList.remove('collapsed');
                    }
                }
            }
        }

        function saveCollapseStates() {
            try {
                localStorage.setItem(collapseStateStorageKey, JSON.stringify(collapseStates));
            } catch (e) {
                console.error("Error saving collapse states:", e);
            }
        }

        function loadCollapseStates() {
            try {
                const saved = localStorage.getItem(collapseStateStorageKey);
                if (saved) {
                    collapseStates = JSON.parse(saved);
                }
            } catch (e) {
                console.error("Error loading collapse states:", e);
                collapseStates = {};
            }
        }

        // 创建计划行UI
        function createPlanRowUI(rowData, rowIndex, savedRowSpecificData = null) {
            const tr = document.createElement('tr');
            tr.dataset.rowIndex = rowIndex;

            // 如果是组头行，添加特殊样式
            if (isGroupHeader(rowIndex)) {
                tr.classList.add('group-header-row');
            }

            // 添加折叠按钮列
            const collapseCell = document.createElement('td');
            if (isGroupHeader(rowIndex)) {
                const collapseButton = document.createElement('div');
                collapseButton.classList.add('collapse-toggle');
                const groupIndex = getGroupIndex(rowIndex);
                const isCollapsed = collapseStates[groupIndex] || false;
                collapseButton.textContent = isCollapsed ? '+' : '-';
                collapseButton.title = isCollapsed ? '展开此组' : '折叠此组';
                if (isCollapsed) {
                    collapseButton.classList.add('collapsed');
                }

                collapseButton.addEventListener('click', () => {
                    toggleGroupCollapse(groupIndex);
                });

                collapseCell.appendChild(collapseButton);
            } else {
                collapseCell.innerHTML = ''; // 非组头行留空
            }
            tr.appendChild(collapseCell);

            const seqCell = document.createElement('td');
            console.log('创建序号单元格，rowData:', rowData, 'seq值:', rowData.seq); // 调试信息
            seqCell.textContent = rowData.seq || (rowIndex + 1); // 如果seq为空，使用rowIndex+1
            tr.appendChild(seqCell);

            const dateCell = document.createElement('td');
            const dateInput = document.createElement('input');
            dateInput.type = 'text';
            dateInput.classList.add('study-date-input');
            dateInput.value = (savedRowSpecificData && savedRowSpecificData.date !== undefined) ? savedRowSpecificData.date : (rowData.date || "");
            dateInput.placeholder = "日期";

            // 添加TAB键纵向移动功能
            dateInput.addEventListener('keydown', handleVerticalTabNavigation);

            dateCell.appendChild(dateInput);
            tr.appendChild(dateCell);

            const contentCell = document.createElement('td');
            contentCell.classList.add('study-content-cell');

            if (savedRowSpecificData && savedRowSpecificData.studyContent) {
                savedRowSpecificData.studyContent.forEach(num => {
                    createAndAppendDroppedItemTag(contentCell, num, true);
                });
            }

            contentCell.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.stopPropagation();
                contentCell.classList.add('drag-over');
                e.dataTransfer.dropEffect = 'copy'; // 改为copy，与dragstart保持一致
                console.log('拖拽经过学习内容单元格'); // 调试信息
            });

            contentCell.addEventListener('dragenter', (e) => {
                e.preventDefault();
                e.stopPropagation();
                contentCell.classList.add('drag-over');
            });

            contentCell.addEventListener('dragleave', (e) => {
                // 只有当离开的是contentCell本身时才移除样式
                if (!contentCell.contains(e.relatedTarget)) {
                    contentCell.classList.remove('drag-over');
                }
            });

            contentCell.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                contentCell.classList.remove('drag-over');
                const droppedNumberStr = e.dataTransfer.getData('text/plain');

                if (droppedNumberStr) {
                    // 允许在同一单元格中添加多个相同或不同的学习单元
                    saveToHistory();
                    createAndAppendDroppedItemTag(contentCell, droppedNumberStr, false);
                    console.log('成功拖拽:', droppedNumberStr); // 调试信息
                }
                // 不再更新拖拽按钮状态，允许多次拖拽
            });

            tr.appendChild(contentCell);

            reviewIntervalHeaders.forEach((header, colIndex) => {
                const reviewTd = document.createElement('td');
                const reviewContentDiv = document.createElement('div');
                reviewContentDiv.classList.add('review-cell-content');

                const input = document.createElement('input');
                input.type = 'text';
                input.placeholder = '';

                // 添加TAB键纵向移动功能
                input.addEventListener('keydown', handleVerticalTabNavigation);

                const btn = document.createElement('button');
                btn.textContent = '完成';

                if (savedRowSpecificData && savedRowSpecificData.reviewCells && savedRowSpecificData.reviewCells[colIndex]) {
                    const cellState = savedRowSpecificData.reviewCells[colIndex];
                    input.value = cellState.text;
                    if (cellState.done) {
                        btn.disabled = true;
                        input.readOnly = true;
                    }
                }

                btn.addEventListener('click', function() {
                    saveToHistory();
                    this.disabled = true;
                    input.readOnly = true;
                });

                reviewContentDiv.appendChild(input);
                reviewContentDiv.appendChild(btn);
                reviewTd.appendChild(reviewContentDiv);
                tr.appendChild(reviewTd);
            });

            return tr;
        }

        function generateStudyPlanTable(planDataArray = initialPlanTableStructure, savedPlanObject = null) {
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            if (!studyPlanTableBody) {
                console.error('找不到表格tbody元素');
                return;
            }

            console.log('生成学习计划表，行数:', planDataArray.length); // 调试信息
            studyPlanTableBody.innerHTML = '';
            planDataArray.forEach((rowData, rowIndex) => {
                const specificSavedDataForRow = (savedPlanObject && savedPlanObject.tableData && savedPlanObject.tableData[rowIndex])
                                              ? savedPlanObject.tableData[rowIndex]
                                              : rowData;
                const tr = createPlanRowUI(rowData, rowIndex, specificSavedDataForRow);
                studyPlanTableBody.appendChild(tr);
            });

            console.log('表格生成完成，实际行数:', studyPlanTableBody.children.length); // 调试信息

            // 应用折叠状态
            applyCollapseStates();
            updatePlanRowButtons();
        }

        function applyCollapseStates() {
            // 为每个组应用折叠状态
            Object.keys(collapseStates).forEach(groupIndex => {
                updateGroupVisibility(parseInt(groupIndex));
                updateCollapseButton(parseInt(groupIndex));
            });
        }

        function addStudyPlanRow() {
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            if (!studyPlanTableBody) return;

            saveToHistory();
            const newSeq = studyPlanTableBody.rows.length + 1;
            const newRowData = { seq: newSeq, date: "" };
            const tr = createPlanRowUI(newRowData, studyPlanTableBody.rows.length, null);
            studyPlanTableBody.appendChild(tr);
            tr.scrollIntoView({ behavior: 'smooth', block: 'end' });
            updatePlanRowButtons();
        }

        function removeStudyPlanRow() {
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            if (!studyPlanTableBody) return;

            const rows = studyPlanTableBody.rows;
            if (rows.length <= 1) {
                alert('至少需要保留一行学习计划！');
                return;
            }

            if (confirm('确定要删除最后一行学习计划吗？')) {
                saveToHistory();

                // 删除最后一行（不需要释放拖拽单元，因为允许多次拖拽）
                const lastRow = rows[rows.length - 1];
                studyPlanTableBody.removeChild(lastRow);
                updatePlanRowButtons();
            }
        }

        function updatePlanRowButtons() {
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            const removePlanRowButton = document.getElementById('remove-plan-row-button');

            if (studyPlanTableBody && removePlanRowButton) {
                // 如果只有一行或没有行，禁用删除按钮
                removePlanRowButton.disabled = studyPlanTableBody.rows.length <= 1;
            }
        }

        function createAndAppendDroppedItemTag(cell, number, isLoadedFromSave = false) {
            const itemTag = document.createElement('span');
            itemTag.classList.add('dropped-item-tag');
            itemTag.dataset.value = number;
            itemTag.textContent = number;

            // 添加点击功能，可以直接启动练习
            itemTag.addEventListener('click', () => {
                // 从"第X组"格式中提取组号
                const match = number.match(/第(\d+)组/);
                if (match) {
                    const groupNumber = parseInt(match[1]);
                    startPracticeFromGroup(groupNumber);
                }
            });

            const removeBtn = document.createElement('span');
            removeBtn.classList.add('remove-item-btn');
            removeBtn.textContent = 'x';
            removeBtn.title = '移除此单元';
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止触发父元素的点击事件
                saveToHistory();
                itemTag.remove();
                // 不再更新拖拽按钮状态，因为允许多次拖拽
            });

            itemTag.appendChild(removeBtn);
            cell.appendChild(itemTag);

            // 不再需要更新拖拽按钮状态，因为允许多次拖拽
        }

        // 保存和加载功能
        function savePlanState() {
            console.log('保存按钮被点击了！'); // 调试信息

            try {
                const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
                if (!studyPlanTableBody) {
                    console.error('找不到学习计划表格体！');
                    alert('错误：找不到学习计划表格！');
                    return;
                }

                console.log('找到表格体，行数：', studyPlanTableBody.querySelectorAll('tr').length);

                const planStateToSave = {
                    tableData: [],
                    draggableNumbersState: draggableNumbersState || {},
                    timestamp: new Date().toISOString()
                };

                let rowCount = 0;
                studyPlanTableBody.querySelectorAll('tr').forEach((tr, index) => {
                    try {
                        const rowData = {
                            seq: tr.cells[1] ? parseInt(tr.cells[1].textContent, 10) : index + 1, // 序号在第1列（cells[1]）
                            date: '',
                            studyContent: [],
                            reviewCells: []
                        };

                        // 获取日期
                        const dateInput = tr.cells[2] ? tr.cells[2].querySelector('input.study-date-input') : null; // 日期在第2列（cells[2]）
                        if (dateInput) {
                            rowData.date = dateInput.value || '';
                        }

                        // 获取学习内容
                        if (tr.cells[3]) { // 学习内容在第3列（cells[3]）
                            tr.cells[3].querySelectorAll('.dropped-item-tag').forEach(tag => {
                                if (tag.dataset.value) {
                                    rowData.studyContent.push(tag.dataset.value);
                                }
                            });
                        }

                        // 获取复习内容
                        tr.querySelectorAll('.review-cell-content').forEach((rcDiv) => {
                            const input = rcDiv.querySelector('input[type="text"]');
                            const button = rcDiv.querySelector('button');
                            if (input) {
                                rowData.reviewCells.push({
                                    text: input.value || '',
                                    done: button ? button.disabled : false
                                });
                            }
                        });

                        planStateToSave.tableData.push(rowData);
                        rowCount++;
                    } catch (rowError) {
                        console.error('处理行数据时出错:', rowError);
                    }
                });

                console.log('处理了', rowCount, '行数据');
                console.log('准备保存的数据：', planStateToSave);

                // 检查 localStorage 是否可用
                if (typeof(Storage) === "undefined") {
                    throw new Error("浏览器不支持 localStorage");
                }

                const dataString = JSON.stringify(planStateToSave);
                console.log('数据字符串长度：', dataString.length);

                localStorage.setItem(planTableStorageKey, dataString);
                console.log('数据已成功保存到 localStorage，键名：', planTableStorageKey);

                // 验证保存
                const savedData = localStorage.getItem(planTableStorageKey);
                if (savedData) {
                    console.log('验证：数据确实已保存，长度：', savedData.length);
                    alert('学习计划已保存成功！');
                } else {
                    throw new Error('保存验证失败');
                }

            } catch (e) {
                console.error("保存计划时出错:", e);
                alert('学习计划保存失败：' + e.message);
            }
        }

        function loadPlanState() {
            const savedDataString = localStorage.getItem(planTableStorageKey);
            if (savedDataString) {
                try {
                    const savedData = JSON.parse(savedDataString);

                    if (savedData.draggableNumbersState) {
                        Object.assign(draggableNumbersState, savedData.draggableNumbersState);
                    } else {
                        draggableNumbersState = {};
                    }
                    generateDraggableNumbers();

                    const tableDataForRendering = (savedData.tableData && savedData.tableData.length > 0)
                                                 ? savedData.tableData
                                                 : JSON.parse(JSON.stringify(initialPlanTableStructure));
                    generateStudyPlanTable(tableDataForRendering, savedData);
                    console.log("学习计划已加载。");
                } catch (e) {
                    console.error("Error loading or parsing saved plan data:", e);
                    resetPlanUI(false);
                }
            } else {
                generateDraggableNumbers();
                generateStudyPlanTable(JSON.parse(JSON.stringify(initialPlanTableStructure)));
            }
        }

        function resetPlanUI(showAlert = true) {
            if (showAlert && !confirm("确定要重置学习计划表吗？此操作不可撤销。")) {
                return;
            }
            localStorage.removeItem(planTableStorageKey);
            localStorage.removeItem(collapseStateStorageKey);

            draggableNumbersState = {};
            collapseStates = {};
            generateDraggableNumbers();

            let freshInitialPlan = JSON.parse(JSON.stringify(initialPlanTableStructure));
            generateStudyPlanTable(freshInitialPlan);

            if (showAlert) alert("学习计划表已重置。");
        }

        function printPlan() {
            window.print();
        }

        // 自动填充功能（已禁用学习内容自动填充）
        function autoFillStudyContent() {
            // 不再自动填充学习内容，让用户手动拖拽
            return;
        }

        function autoFillFirstDayReview() {
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            if (!studyPlanTableBody) return;

            // 计算总组数
            const totalGroups = typeof WORDS_DATA !== 'undefined' ? Math.ceil(WORDS_DATA.length / 100) : 5;
            const rows = studyPlanTableBody.querySelectorAll('tr');

            rows.forEach((row, rowIndex) => {
                if (rowIndex < totalGroups) {
                    const firstDayCell = row.cells[3]; // "第1天"是第4列（索引3）
                    if (firstDayCell) {
                        const reviewContent = firstDayCell.querySelector('.review-cell-content');
                        if (reviewContent) {
                            const input = reviewContent.querySelector('input[type="text"]');
                            if (input && !input.value) {
                                input.value = `第${rowIndex + 1}组`; // 填入"第1组"、"第2组"等
                            }
                        }
                    }
                }
            });
        }



        // TAB键纵向移动功能
        function handleVerticalTabNavigation(e) {
            if (e.key === 'Tab') {
                e.preventDefault();

                const currentInput = e.target;
                const currentRow = currentInput.closest('tr');
                const currentCell = currentInput.closest('td');
                const table = currentRow.closest('table');
                const tbody = table.querySelector('tbody');

                // 获取当前单元格在行中的索引
                const cellIndex = Array.from(currentRow.cells).indexOf(currentCell);

                // 获取当前行在表格中的索引
                const rowIndex = Array.from(tbody.rows).indexOf(currentRow);

                let targetRow, targetInput;

                if (e.shiftKey) {
                    // Shift+Tab: 向上移动
                    if (rowIndex > 0) {
                        targetRow = tbody.rows[rowIndex - 1];
                    } else {
                        // 如果已经是第一行，跳到最后一行
                        targetRow = tbody.rows[tbody.rows.length - 1];
                    }
                } else {
                    // Tab: 向下移动
                    if (rowIndex < tbody.rows.length - 1) {
                        targetRow = tbody.rows[rowIndex + 1];
                    } else {
                        // 如果已经是最后一行，跳到第一行
                        targetRow = tbody.rows[0];
                    }
                }

                // 在目标行中找到相同列的输入框
                const targetCell = targetRow.cells[cellIndex];
                if (targetCell) {
                    if (cellIndex === 2) {
                        // 学习日期列（现在是第2列，因为有折叠按钮列）
                        targetInput = targetCell.querySelector('.study-date-input');
                    } else if (cellIndex >= 4) {
                        // 复习列（现在从第4列开始，因为有折叠按钮列和序号列）
                        targetInput = targetCell.querySelector('.review-cell-content input[type="text"]');
                    }

                    if (targetInput) {
                        targetInput.focus();
                        targetInput.select(); // 选中文本，方便替换
                    }
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
