<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初中单词背诵练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #1a1a1a;
            color: #e0e0e0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            flex: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #2d3748, #4a5568);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            color: #90cdf4;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .header p {
            color: #cbd5e0;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            align-items: center;
        }

        .group-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .group-selector label {
            color: #a0aec0;
            font-weight: 500;
        }

        .group-selector select {
            background-color: #2d3748;
            color: #e2e8f0;
            border: 2px solid #4a5568;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .group-selector select:focus {
            border-color: #90cdf4;
            box-shadow: 0 0 0 3px rgba(144, 205, 244, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
        }

        .btn:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #68d391, #48bb78);
        }

        .btn.secondary:hover {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }

        .btn.danger {
            background: linear-gradient(135deg, #fc8181, #e53e3e);
        }

        .btn.danger:hover {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }

        .practice-area {
            background-color: #2d3748;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            text-align: center;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .word-info {
            margin-bottom: 30px;
        }

        .word-info .chinese {
            font-size: 2.5em;
            color: #90cdf4;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .word-info .part-of-speech {
            font-size: 1.3em;
            color: #fbb6ce;
            margin-bottom: 20px;
            font-style: italic;
        }

        .input-area {
            margin-bottom: 25px;
        }

        .input-area input {
            background-color: #1a202c;
            color: #e2e8f0;
            border: 2px solid #4a5568;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 1.5em;
            width: 100%;
            max-width: 400px;
            text-align: center;
            outline: none;
            transition: all 0.3s ease;
        }

        .input-area input:focus {
            border-color: #90cdf4;
            box-shadow: 0 0 0 3px rgba(144, 205, 244, 0.1);
        }

        .feedback {
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: 500;
            min-height: 30px;
        }

        .feedback.correct {
            color: #68d391;
        }

        .feedback.incorrect {
            color: #fc8181;
            max-width: 100%;
            overflow-wrap: break-word;
        }

        .feedback.incorrect .phonetic {
            color: #fbb6ce;
            font-style: italic;
            margin-left: 8px;
        }

        .feedback.incorrect .example-box {
            background-color: #2d3748;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
            padding: 12px;
            margin: 10px 0;
            text-align: left;
        }

        .feedback.incorrect .example-label {
            color: #a0aec0;
            font-size: 0.9em;
            margin-bottom: 5px;
            font-weight: normal;
        }

        .feedback.incorrect .example-text {
            color: #e2e8f0;
            line-height: 1.4;
            font-weight: normal;
        }

        .progress {
            background-color: #2d3748;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border: 1px solid #4a5568;
        }

        .progress-bar {
            background-color: #4a5568;
            height: 12px;
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            background: linear-gradient(90deg, #4299e1, #90cdf4);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 6px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #90cdf4;
        }

        .stat-label {
            color: #a0aec0;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .file-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .file-label {
            background: linear-gradient(135deg, #805ad5, #6b46c1);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(128, 90, 213, 0.3);
        }

        .file-label:hover {
            background: linear-gradient(135deg, #6b46c1, #553c9a);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(128, 90, 213, 0.4);
        }

        .welcome-screen {
            text-align: center;
            color: #a0aec0;
        }

        .welcome-screen h2 {
            color: #90cdf4;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .welcome-screen p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .word-info .chinese {
                font-size: 2em;
            }
            
            .input-area input {
                font-size: 1.2em;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>初中单词背诵练习</h1>
            <p>第二轮单词练习 - 共442个单词</p>
        </div>

        <div class="controls">
            <div class="group-selector">
                <label for="groupSelect">选择练习组:</label>
                <select id="groupSelect">
                    <option value="">请选择...</option>
                </select>
            </div>
            <button class="btn" id="startBtn" onclick="startPractice()">开始练习</button>
            <button class="btn secondary" id="exportBtn" onclick="exportWrongWords()" style="display: none;">导出错误单词</button>
            <button class="btn secondary" id="backBtn" onclick="backToHome()" style="display: none;">返回首页</button>
        </div>

        <div class="file-controls" id="fileControls">
            <input type="file" id="importFile" class="file-input" accept=".json" multiple onchange="importWords()">
            <label for="importFile" class="file-label">导入单词（可选择多个文件）</label>
        </div>

        <div class="practice-area" id="practiceArea">
            <div class="welcome-screen">
                <h2>欢迎使用单词背诵练习</h2>
                <p>📚 选择一个练习组开始背单词</p>
                <p>💡 每组包含100个单词（最后一组42个）</p>
                <p>✨ 答错的单词会重复出现，直到答对为止</p>
                <p>📊 练习结束后可以导出错误单词记录</p>
            </div>
        </div>

        <div class="progress" id="progressArea" style="display: none;">
            <div>练习进度</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalWords">0</div>
                    <div class="stat-label">总单词数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedWords">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="wrongWords">0</div>
                    <div class="stat-label">错误次数</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入单词数据 -->
    <script src="words_data.js"></script>

    <script>
        
        // 练习状态
        let currentGroup = [];
        let practiceWords = [];
        let currentWordIndex = 0;
        let currentWord = null;
        let isPracticing = false;
        let wrongWordsCount = {};
        let completedWordsSet = new Set();
        let isWaitingForConfirm = false; // 是否等待用户确认
        let currentGroupNumber = 0; // 当前练习组号
        
        // 初始化页面
        function initializePage() {
            populateGroupSelector();
            setupEventListeners();
        }
        
        // 填充组选择器
        function populateGroupSelector() {
            const select = document.getElementById('groupSelect');
            const totalGroups = Math.ceil(WORDS_DATA.length / 100);
            
            for (let i = 1; i <= totalGroups; i++) {
                const option = document.createElement('option');
                const startIndex = (i - 1) * 100;
                const endIndex = Math.min(i * 100, WORDS_DATA.length);
                const count = endIndex - startIndex;
                
                option.value = i;
                option.textContent = `第${i}组 (${startIndex + 1}-${endIndex}, 共${count}个单词)`;
                select.appendChild(option);
            }
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            const input = document.getElementById('answerInput');
            if (input) {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        checkAnswer();
                    }
                });
            }
        }
        
        // 开始练习
        function startPractice() {
            const groupSelect = document.getElementById('groupSelect');
            const selectedGroup = parseInt(groupSelect.value);

            if (!selectedGroup) {
                alert('请先选择一个练习组！');
                return;
            }

            // 获取选中组的单词
            const startIndex = (selectedGroup - 1) * 100;
            const endIndex = Math.min(selectedGroup * 100, WORDS_DATA.length);
            currentGroup = WORDS_DATA.slice(startIndex, endIndex);
            currentGroupNumber = selectedGroup; // 记录当前组号

            // 初始化练习数据
            practiceWords = [...currentGroup];
            currentWordIndex = 0;
            wrongWordsCount = {};
            completedWordsSet = new Set();
            isPracticing = true;
            isWaitingForConfirm = false;

            // 更新UI
            document.getElementById('startBtn').style.display = 'none';
            document.getElementById('exportBtn').style.display = 'inline-block';
            document.getElementById('backBtn').style.display = 'inline-block';
            document.getElementById('fileControls').style.display = 'none';
            document.getElementById('progressArea').style.display = 'block';

            // 开始第一个单词
            showNextWord();
            updateProgress();
        }
        
        // 重置练习状态
        function resetPractice() {
            isPracticing = false;
            currentGroup = [];
            practiceWords = [];
            currentWordIndex = 0;
            currentWord = null;
            isWaitingForConfirm = false;
            currentGroupNumber = 0;

            // 重置UI
            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('exportBtn').style.display = 'none';
            document.getElementById('backBtn').style.display = 'none';
            document.getElementById('fileControls').style.display = 'flex';
            document.getElementById('progressArea').style.display = 'none';

            // 显示欢迎界面
            showWelcomeScreen();
        }

        // 返回首页
        function backToHome() {
            if (confirm('确定要返回首页吗？当前练习进度将会丢失。')) {
                resetPractice();
            }
        }
        
        // 显示下一个单词
        function showNextWord() {
            if (!isPracticing || practiceWords.length === 0) {
                showCompletionScreen();
                return;
            }

            // 智能随机选择单词（答错次数多的单词有更高概率出现）
            currentWordIndex = selectNextWordIndex();
            currentWord = practiceWords[currentWordIndex];
            showCurrentWord();
        }

        // 智能选择下一个单词的索引
        function selectNextWordIndex() {
            // 如果只有一个单词，直接返回
            if (practiceWords.length === 1) {
                return 0;
            }

            // 创建权重数组，答错次数多的单词权重更高
            const weights = practiceWords.map(word => {
                const errorCount = wrongWordsCount[word.id] || 0;
                // 基础权重为1，每答错一次增加2的权重
                return 1 + (errorCount * 2);
            });

            // 计算总权重
            const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

            // 生成随机数
            let random = Math.random() * totalWeight;

            // 根据权重选择单词
            for (let i = 0; i < weights.length; i++) {
                random -= weights[i];
                if (random <= 0) {
                    return i;
                }
            }

            // 兜底：返回最后一个索引
            return weights.length - 1;
        }
        
        // 显示当前单词
        function showCurrentWord() {
            if (!currentWord) return;

            // 重置等待确认状态
            isWaitingForConfirm = false;

            const practiceArea = document.getElementById('practiceArea');
            practiceArea.innerHTML = `
                <div class="word-info">
                    <div class="chinese">${currentWord.chinese}</div>
                    <div class="part-of-speech">${currentWord.partOfSpeech}</div>
                </div>
                <div class="input-area">
                    <input type="text" id="answerInput" placeholder="请输入英文单词..." autocomplete="off">
                </div>
                <div class="feedback" id="feedback"></div>
            `;

            // 重新设置事件监听器
            setupEventListeners();

            // 聚焦输入框
            setTimeout(() => {
                document.getElementById('answerInput').focus();
            }, 100);
        }
        
        // 检查答案
        function checkAnswer() {
            // 如果正在等待确认，则跳转到下一个单词
            if (isWaitingForConfirm) {
                showNextWord();
                updateProgress();
                isWaitingForConfirm = false;
                return;
            }

            const input = document.getElementById('answerInput');
            const feedback = document.getElementById('feedback');
            const userAnswer = input.value.trim().toLowerCase();
            const correctAnswer = currentWord.english.toLowerCase();

            if (userAnswer === correctAnswer) {
                // 答对了
                feedback.textContent = '✅ 正确！';
                feedback.className = 'feedback correct';

                // 从练习列表中移除这个单词
                practiceWords.splice(currentWordIndex, 1);
                completedWordsSet.add(currentWord.id);

                // 1秒后显示下一个单词
                setTimeout(() => {
                    showNextWord();
                    updateProgress();
                }, 1000);

            } else {
                // 答错了 - 显示详细的正确答案信息
                feedback.innerHTML = `
                    <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                        <div style="margin-bottom: 10px;">
                            ❌ <strong style="color: #fc8181;">错误！</strong>
                        </div>
                        <div style="margin-bottom: 15px; font-size: 1.1em;">
                            <strong style="color: #90cdf4;">正确答案：</strong>
                            <span style="color: #e2e8f0; font-size: 1.2em;">${currentWord.english}</span>
                        </div>
                        <div style="margin-bottom: 10px; padding: 10px; background-color: #2d3748; border-radius: 8px; border-left: 4px solid #4299e1;">
                            <div style="color: #a0aec0; font-size: 0.9em; margin-bottom: 5px;">例句：</div>
                            <div style="color: #e2e8f0; line-height: 1.4;">${currentWord.example}</div>
                        </div>
                        <div style="margin-top: 15px; color: #a0aec0; font-size: 0.9em; text-align: center;">
                            <strong>按回车键继续下一个单词</strong>
                        </div>
                    </div>
                `;
                feedback.className = 'feedback incorrect';

                // 记录错误次数
                if (!wrongWordsCount[currentWord.id]) {
                    wrongWordsCount[currentWord.id] = 0;
                }
                wrongWordsCount[currentWord.id]++;

                // 清空输入框并设置等待确认状态
                input.value = '';
                input.placeholder = '按回车键继续...';
                isWaitingForConfirm = true;
            }
        }
        
        // 更新进度
        function updateProgress() {
            const totalWords = currentGroup.length;
            const completed = completedWordsSet.size;
            const wrong = Object.values(wrongWordsCount).reduce((sum, count) => sum + count, 0);
            const remaining = practiceWords.length;

            document.getElementById('totalWords').textContent = totalWords;
            document.getElementById('completedWords').textContent = completed;
            document.getElementById('wrongWords').textContent = wrong;

            const progress = (completed / totalWords) * 100;
            document.getElementById('progressFill').style.width = progress + '%';

            // 在控制台输出调试信息（可选）
            if (isPracticing && practiceWords.length > 0) {
                console.log(`练习进度: 总计${totalWords}, 完成${completed}, 剩余${remaining}, 错误${wrong}次`);
                console.log('当前练习列表:', practiceWords.map(w => `${w.english}(错误${wrongWordsCount[w.id] || 0}次)`));
            }
        }
        

        // 显示完成界面
        function showCompletionScreen() {
            const practiceArea = document.getElementById('practiceArea');
            const totalWords = currentGroup.length;
            const wrongCount = Object.keys(wrongWordsCount).length;

            practiceArea.innerHTML = `
                <div class="welcome-screen">
                    <h2>🎉 练习完成！</h2>
                    <p>总共练习了 ${totalWords} 个单词</p>
                    <p>答错的单词有 ${wrongCount} 个</p>
                    ${wrongCount > 0 ? '<p>可以点击"导出错误单词"按钮保存错误记录</p>' : '<p>全部答对，太棒了！</p>'}
                </div>
            `;

            // 重置按钮状态
            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('backBtn').style.display = 'inline-block';
            document.getElementById('fileControls').style.display = 'flex';
            isPracticing = false;
        }
        
        // 显示欢迎界面
        function showWelcomeScreen() {
            const practiceArea = document.getElementById('practiceArea');
            practiceArea.innerHTML = `
                <div class="welcome-screen">
                    <h2>欢迎使用单词背诵练习</h2>
                    <p>📚 选择一个练习组开始背单词</p>
                    <p>💡 每组包含100个单词（最后一组42个）</p>
                    <p>✨ 答错的单词会重复出现，直到答对为止</p>
                    <p>📊 练习结束后可以导出错误单词记录</p>
                </div>
            `;
        }
        
        // 导出错误单词
        function exportWrongWords() {
            if (Object.keys(wrongWordsCount).length === 0) {
                alert('没有错误单词需要导出！');
                return;
            }

            const wrongWords = [];
            for (const [wordId, count] of Object.entries(wrongWordsCount)) {
                const word = WORDS_DATA.find(w => w.id == wordId);
                if (word) {
                    wrongWords.push({
                        ...word,
                        errorCount: count
                    });
                }
            }

            const timestamp = new Date().toISOString().split('T')[0];

            // 生成文件名
            let fileName;
            if (currentGroupNumber > 0) {
                fileName = `wrong_words_第${currentGroupNumber}组_${timestamp}.json`;
            } else {
                fileName = `wrong_words_自定义_${timestamp}.json`;
            }

            // 导出JSON格式
            const exportData = {
                exportTime: new Date().toISOString(),
                groupNumber: currentGroupNumber,
                groupType: currentGroupNumber > 0 ? `第${currentGroupNumber}组` : '自定义导入',
                totalWords: currentGroup.length,
                wrongWordsCount: wrongWords.length,
                words: wrongWords
            };

            const jsonBlob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const jsonUrl = URL.createObjectURL(jsonBlob);
            const jsonLink = document.createElement('a');
            jsonLink.href = jsonUrl;
            jsonLink.download = fileName;
            document.body.appendChild(jsonLink);
            jsonLink.click();
            document.body.removeChild(jsonLink);
            URL.revokeObjectURL(jsonUrl);

            alert(`错误单词记录已导出！\n已生成JSON格式文件。`);
        }
        
        // 导入单词
        function importWords() {
            const files = document.getElementById('importFile').files;
            if (!files || files.length === 0) return;

            let allWords = [];
            let processedFiles = 0;
            let totalFiles = files.length;
            let fileNames = [];

            // 处理每个文件
            Array.from(files).forEach((file, fileIndex) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const importData = JSON.parse(e.target.result);

                        // 验证数据格式
                        if (!importData.words || !Array.isArray(importData.words)) {
                            console.warn(`文件 ${file.name} 格式错误：缺少words数组`);
                            processedFiles++;
                            checkAllFilesProcessed();
                            return;
                        }

                        // 验证单词格式
                        const validWords = importData.words.filter(word =>
                            word.english && word.chinese && word.partOfSpeech && word.example
                        );

                        if (validWords.length > 0) {
                            // 为导入的单词分配新的ID，避免重复
                            validWords.forEach((word, index) => {
                                word.id = (fileIndex + 1) * 1000 + index; // 使用文件索引避免ID冲突
                                word.sourceFile = file.name; // 记录来源文件
                            });

                            allWords = allWords.concat(validWords);
                            fileNames.push(file.name);
                        }

                        processedFiles++;
                        checkAllFilesProcessed();

                    } catch (error) {
                        console.error(`文件 ${file.name} 解析失败:`, error);
                        processedFiles++;
                        checkAllFilesProcessed();
                    }
                };

                reader.readAsText(file);
            });

            function checkAllFilesProcessed() {
                if (processedFiles === totalFiles) {
                    if (allWords.length === 0) {
                        alert('所有文件中都没有有效的单词数据');
                        return;
                    }

                    // 去重处理（基于英文单词）
                    const uniqueWords = [];
                    const seenWords = new Set();

                    allWords.forEach(word => {
                        const key = word.english.toLowerCase();
                        if (!seenWords.has(key)) {
                            seenWords.add(key);
                            uniqueWords.push(word);
                        }
                    });

                    // 创建自定义练习组
                    currentGroup = uniqueWords;
                    practiceWords = [...currentGroup];
                    currentWordIndex = 0;
                    wrongWordsCount = {};
                    completedWordsSet = new Set();
                    isPracticing = true;
                    isWaitingForConfirm = false;
                    currentGroupNumber = 0; // 自定义导入设置为0

                    // 更新UI
                    document.getElementById('startBtn').style.display = 'none';
                    document.getElementById('exportBtn').style.display = 'inline-block';
                    document.getElementById('backBtn').style.display = 'inline-block';
                    document.getElementById('fileControls').style.display = 'none';
                    document.getElementById('progressArea').style.display = 'block';

                    // 开始练习
                    showNextWord();
                    updateProgress();

                    const duplicateCount = allWords.length - uniqueWords.length;
                    let message = `成功导入 ${uniqueWords.length} 个单词！\n`;
                    message += `来源文件: ${fileNames.join(', ')}\n`;
                    if (duplicateCount > 0) {
                        message += `已自动去除 ${duplicateCount} 个重复单词。`;
                    }

                    alert(message);
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
