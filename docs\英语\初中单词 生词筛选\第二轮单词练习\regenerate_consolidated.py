#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成正确的 consolidated_words.txt 文件
按照正确的编号顺序整合所有文件
"""

import re
import os
from datetime import datetime

def parse_word_file(file_path):
    """解析单个单词文件"""
    words = []
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return words
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式匹配每个单词条目
    pattern = r'(\d+)\.\s+([^(]+)\s*\(([^)]+)\)\s*\n\s*中文:\s*([^\n]+)\s*\n\s*例句:\s*([^\n]+)'
    
    matches = re.findall(pattern, content)
    
    for match in matches:
        word_id = int(match[0])
        english = match[1].strip()
        part_of_speech = match[2].strip()
        chinese = match[3].strip()
        example = match[4].strip()
        
        words.append({
            'english': english,
            'chinese': chinese,
            'partOfSpeech': part_of_speech,
            'example': example
        })
    
    return words

def generate_consolidated_file():
    """生成整合后的文件"""
    
    # 文件列表和对应的编号范围
    file_configs = [
        # 原有文件 (1-759)
        ('A.txt', 1, 759),
        # 新增文件
        ('P.txt', 760, 875),      # 116个
        ('QR.txt', 876, 965),     # 90个
        ('S.txt', 966, 1132),     # 167个
        ('T.txt', 1133, 1208),    # 76个
        ('UV.txt', 1209, 1245),   # 37个
        ('W.txt', 1246, 1295),    # 50个
        ('XYZ.txt', 1296, 1298),  # 3个
        ('Others.txt', 1299, 1350) # 52个 (去除重复的Wednesday)
    ]
    
    all_words = []
    current_id = 1
    
    print("开始处理文件...")
    
    for filename, start_id, end_id in file_configs:
        print(f"处理 {filename}...")
        
        # 特殊处理：读取现有的consolidated文件中的A-O部分
        if filename == 'A.txt':
            # 读取现有consolidated文件中的1-759号单词
            with open('consolidated_words.txt', 'r', encoding='utf-8') as f:
                content = f.read()
            
            pattern = r'(\d+)\.\s+([^(]+)\s*\(([^)]+)\)\s*\n\s*中文:\s*([^\n]+)\s*\n\s*例句:\s*([^\n]+)'
            matches = re.findall(pattern, content)
            
            for match in matches:
                word_id = int(match[0])
                if 1 <= word_id <= 759:  # 只取A-O的单词
                    english = match[1].strip()
                    part_of_speech = match[2].strip()
                    chinese = match[3].strip()
                    example = match[4].strip()
                    
                    all_words.append({
                        'id': word_id,
                        'english': english,
                        'chinese': chinese,
                        'partOfSpeech': part_of_speech,
                        'example': example
                    })
            
            current_id = 760
            continue
        
        # 处理其他文件
        words = parse_word_file(filename)
        
        # 特殊处理Others.txt，去除重复的Wednesday
        if filename == 'Others.txt':
            words = [w for w in words if w['english'].strip().lower() != 'wednesday']
        
        # 添加编号
        for word in words:
            if current_id <= end_id:
                word['id'] = current_id
                all_words.append(word)
                current_id += 1
        
        print(f"  {filename}: 添加了 {len(words)} 个单词")
    
    # 按ID排序
    all_words.sort(key=lambda x: x['id'])
    
    # 生成新的consolidated文件
    output_file = 'consolidated_words_new.txt'
    
    with open(output_file, 'w', encoding='utf-8') as f:
        # 写入文件头
        f.write('Consolidated Word List\n')
        f.write(f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
        f.write('Source files: A.txt, B.txt, C.txt, D.txt, E.txt, F.txt, G.txt, H.txt, I.txt, JK.txt, L.txt, M.txt, N.txt, O.txt, P.txt, QR.txt, S.txt, T.txt, UV.txt, W.txt, XYZ.txt, Others.txt\n')
        f.write(f'Total words: {len(all_words)}\n')
        f.write('\n')
        f.write('Consolidated word list:\n')
        f.write('\n')
        
        # 写入所有单词
        for word in all_words:
            f.write(f'{word["id"]}. {word["english"]} ({word["partOfSpeech"]})\n')
            f.write(f'   中文: {word["chinese"]}\n')
            f.write(f'   例句: {word["example"]}\n')
            f.write('\n')
    
    print(f"\n生成完成！")
    print(f"输出文件: {output_file}")
    print(f"总单词数: {len(all_words)}")
    print(f"最后编号: {all_words[-1]['id'] if all_words else 0}")

if __name__ == '__main__':
    generate_consolidated_file()
