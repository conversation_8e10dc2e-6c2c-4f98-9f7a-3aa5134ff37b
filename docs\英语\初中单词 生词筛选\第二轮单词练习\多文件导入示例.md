# 多文件导入功能说明

## 🎯 使用场景

### 场景1：合并多次练习的错误记录
```
第一次练习：wrong_words_2025-07-20.json (15个错误单词)
第二次练习：wrong_words_2025-07-21.json (12个错误单词)
第三次练习：wrong_words_2025-07-22.j<PERSON> (8个错误单词)

→ 一次性导入所有文件，系统自动合并为35个单词（去重后可能更少）
```

### 场景2：导入不同来源的单词文件
```
老师分享：teacher_words.json (50个重点单词)
同学分享：classmate_words.json (30个难词)
自己整理：my_words.json (20个生词)

→ 同时选择三个文件，创建包含100个单词的综合练习
```

## 🔧 操作步骤

### 1. 选择多个文件
- 点击"导入单词（可选择多个文件）"按钮
- 在文件选择对话框中：
  - Windows：按住 Ctrl 键点击多个文件
  - Mac：按住 Cmd 键点击多个文件
- 点击"打开"确认选择

### 2. 系统自动处理
- 逐个解析每个JSON文件
- 验证文件格式和单词数据
- 合并所有有效单词
- 自动去除重复单词（基于英文单词）
- 为每个单词分配唯一ID

### 3. 开始练习
- 显示导入结果统计
- 自动开始综合练习
- 可以导出新的错误记录

## 📊 导入结果示例

```
成功导入 85 个单词！
来源文件: wrong_words_2025-07-20.json, wrong_words_2025-07-21.json, teacher_words.json
已自动去除 15 个重复单词。
```

## 🛡️ 错误处理

### 文件格式错误
- 跳过格式错误的文件
- 在控制台显示警告信息
- 继续处理其他有效文件

### 数据验证
- 检查必需字段：english, chinese, partOfSpeech, example
- 过滤掉不完整的单词数据
- 只导入有效的单词

### 去重逻辑
- 基于英文单词（不区分大小写）
- 保留第一次出现的单词
- 统计并报告去重数量

## 💡 最佳实践

### 文件组织
- 使用有意义的文件名
- 按日期或主题分类
- 定期备份重要的单词文件

### 批量导入
- 一次性导入相关的所有文件
- 避免频繁的单个文件导入
- 利用去重功能整理单词库

### 学习策略
- 合并不同难度的单词文件
- 创建主题相关的练习集合
- 定期复习之前的错误单词
