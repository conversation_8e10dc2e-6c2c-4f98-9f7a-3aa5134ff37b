<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>艾宾浩斯学习计划表 - 2025年暑期</title>
    <style>
        body {
            font-family: 'KaiTi', 'STKaiti', serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            background-color: #fdfaf4;
            padding: 20px;
            box-sizing: border-box;
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        h1 {
            color: #5a4a42;
            margin-bottom: 25px;
            font-weight: normal;
            border-bottom: 2px solid #ccbbaa;
            padding-bottom: 10px;
            text-align: center;
        }

        #selection-container {
            width: 95%;
            max-width: 1300px;
            background-color: #fff;
            border: 1px solid #ccbbaa;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 30px;
            box-sizing: border-box;
            text-align: center;
            margin-bottom: 20px;
        }

        #selection-container h2 { 
            font-weight: normal; 
            color: #6a5a52; 
            margin-top: 0; 
            margin-bottom: 20px; 
            font-size: 1.3em; 
        }
        
        #selection-container h3 { 
            font-weight: normal; 
            color: #7a6a52; 
            margin-top: 20px; 
            margin-bottom: 10px; 
            font-size: 1.1em; 
        }

        .instructions {
            margin-bottom: 20px;
            color: #666;
            line-height: 1.6;
        }

        #study-plan-setup-area { 
            margin-top: 30px; 
            border-top: 1px dashed #ccbbaa; 
            padding-top: 20px; 
        }
        
        #draggable-numbers-title { 
            font-size: 1.1em; 
            color: #444; 
            margin-bottom: 10px; 
            text-align: left; 
        }
        
        #draggable-numbers-container { 
            display: flex; 
            flex-wrap: wrap; 
            gap: 8px; 
            padding: 10px; 
            border: 1px solid #e0d8c8; 
            border-radius: 5px; 
            background-color: #fdfaf4e0; 
            margin-bottom: 20px; 
            min-height: 50px; 
            justify-content: flex-start; 
        }
        
        .draggable-number { 
            padding: 5px 10px; 
            border: 1px solid #ccbbaa; 
            border-radius: 4px; 
            background-color: #fff; 
            cursor: grab; 
            font-family: 'KaiTi', 'STKaiti', serif; 
            font-size: 0.9em; 
            transition: background-color 0.2s, color 0.2s; 
            user-select: none; 
        }
        
        .draggable-number:active { 
            cursor: grabbing; 
        }
        
        .draggable-number.used { 
            background-color: #e0e0e0; 
            color: #888; 
            cursor: not-allowed; 
            border-color: #ccc; 
        }

        #study-plan-table-container { 
            overflow-x: auto; 
        }
        
        #study-plan-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 0.85em;
        }

        #study-plan-table th, #study-plan-table td {
            border: 1px solid #ccbbaa;
            padding: 6px;
            text-align: center;
            min-width: 55px;
            vertical-align: top;
        }

        /* 折叠功能样式 */
        .collapse-toggle {
            background-color: #f0f0f0;
            border: 1px solid #ccbbaa;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 0.8em;
            color: #666;
            user-select: none;
            transition: background-color 0.2s;
            min-width: 30px;
        }

        .collapse-toggle:hover {
            background-color: #e0e0e0;
        }

        .collapse-toggle.collapsed {
            background-color: #d0d0d0;
        }

        .row-group-collapsed {
            display: none;
        }

        .group-header-row {
            background-color: #f7f3e9;
            font-weight: bold;
        }

        .group-header-row td {
            background-color: #f7f3e9;
            border-bottom: 2px solid #ccbbaa;
        }
        
        #study-plan-table th { 
            background-color: #f7f3e9; 
            color: #6a5a52; 
            font-weight: normal; 
            white-space: nowrap; 
        }
        
        .study-date-input { 
            width: 90%; 
            padding: 4px; 
            font-size: 0.95em; 
            border: 1px solid #ccc; 
            border-radius: 3px; 
            text-align: center; 
            box-sizing: border-box; 
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        .study-content-cell { 
            background-color: #fff; 
            min-width: 120px; 
            min-height: 40px; 
            vertical-align: top; 
            text-align: left; 
            padding: 5px; 
        }
        
        .study-content-cell.drag-over { 
            background-color: #e6f7ff; 
            border-style: dashed; 
        }
        
        .dropped-item-tag { 
            display: inline-flex; 
            align-items: center; 
            background-color: #d1e7fd; 
            color: #084298; 
            padding: 3px 6px; 
            border-radius: 4px; 
            margin: 2px; 
            font-size: 0.95em; 
            cursor: default; 
        }
        
        .remove-item-btn { 
            margin-left: 5px; 
            color: #ae2a2a; 
            cursor: pointer; 
            font-weight: bold; 
            padding: 0 3px; 
            border-radius: 50%; 
        }
        
        .remove-item-btn:hover { 
            background-color: #f8d7da; 
        }

        .review-cell-content { 
            display: flex; 
            flex-direction: column; 
            align-items: center; 
            gap: 4px; 
        }
        
        .review-cell-content input[type="text"] { 
            width: 80%; 
            padding: 4px; 
            font-size: 0.9em; 
            border: 1px solid #ccc; 
            border-radius: 3px; 
            text-align: center; 
            box-sizing: border-box; 
            font-family: 'KaiTi', 'STKaiti', serif; 
        }
        
        .review-cell-content button { 
            padding: 3px 8px; 
            font-size: 0.85em; 
            background-color: #8fbc8f; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer; 
            font-family: 'KaiTi', 'STKaiti', serif; 
        }
        
        .review-cell-content button:hover { 
            background-color: #70a070; 
        }
        
        .review-cell-content button:disabled { 
            background-color: #ccc; 
            color: #666; 
            cursor: not-allowed; 
        }
        
        .review-cell-content input[type="text"]:read-only { 
            background-color: #f0f0f0; 
            border-color: #ddd; 
        }
        
        #add-plan-row-button { 
            margin-top: 15px; 
            padding: 8px 15px; 
            background-color: #90ee90; 
            color: #333; 
            border: 1px solid #7cb37c; 
            border-radius: 4px; 
            cursor: pointer; 
            font-family: 'KaiTi', 'STKaiti', serif; 
        }
        
        #add-plan-row-button:hover { 
            background-color: #7ccd7c; 
        }
        
        .plan-action-buttons-container { 
            display: flex; 
            justify-content: center; 
            gap: 10px; 
            margin-top: 20px;
        }
        
        #save-plan-button, #reset-plan-button, #print-button, #undo-button {
            padding: 10px 18px;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        #save-plan-button {
            background-color: #7e8a5f;
        }

        #reset-plan-button {
            background-color: #c86c5d;
        }

        #print-button {
            background-color: #28a745;
        }

        #undo-button {
            background-color: #6c757d;
        }

        #save-plan-button:hover {
            background-color: #6a734f;
        }

        #reset-plan-button:hover {
            background-color: #b05040;
        }

        #print-button:hover {
            background-color: #1e7e34;
        }

        #undo-button:hover {
            background-color: #545b62;
        }

        #undo-button:disabled {
            background-color: #adb5bd;
            cursor: not-allowed;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            /* 隐藏不需要打印的元素 */
            .plan-action-buttons-container,
            #add-plan-row-button,
            #draggable-numbers-title,
            #draggable-numbers-container {
                display: none;
            }

            #selection-container h2,
            .instructions {
                display: none;
            }

            /* 保持表格容器显示 */
            #study-plan-setup-area {
                border-top: none;
                padding-top: 0;
                margin-top: 0;
            }

            #study-plan-table-container {
                margin-top: 0;
            }

            #study-plan-table-container h3 {
                display: none;
            }

            #study-plan-table {
                font-size: 11px;
                margin-top: 0;
                width: 100%;
            }

            #study-plan-table th,
            #study-plan-table td {
                padding: 3px 2px;
                font-size: 10px;
                border: 1px solid #000;
            }

            #study-plan-table th {
                background-color: #f0f0f0 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            h1 {
                margin-bottom: 15px;
                font-size: 1.6em;
                text-align: center;
            }

            #selection-container {
                padding: 10px;
                margin-bottom: 0;
                border: none;
                box-shadow: none;
            }

            /* 确保拖拽的内容在打印时可见 */
            .dropped-item-tag {
                background-color: #e0e0e0 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                border: 1px solid #999;
            }

            .remove-item-btn {
                display: none;
            }

            /* 隐藏复习单元格中的"完成"按钮 */
            .review-cell-content button {
                display: none;
            }

            /* 调整复习单元格布局，去掉按钮后只显示输入框 */
            .review-cell-content {
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            body { 
                padding: 15px; 
                font-size: 14px;
            }
            
            h1 { 
                font-size: 1.5em; 
            }
            
            #selection-container { 
                width: 100%; 
                padding: 15px; 
            }
            
            #study-plan-table { 
                font-size: 0.75em; 
            } 
            
            #study-plan-table th, #study-plan-table td { 
                padding: 4px; 
                min-width: 35px; 
            }
            
            .study-content-cell { 
                min-width: 80px; 
            }
            
            .review-cell-content input[type="text"] { 
                width: 90%; 
                font-size: 0.85em; 
            }
            
            .review-cell-content button { 
                font-size: 0.8em; 
                padding: 2px 5px;
            }

            .plan-action-buttons-container { 
                flex-direction: column; 
                align-items: center; 
            }
            
            #save-plan-button, #reset-plan-button, #print-button { 
                font-size: 1em; 
                padding: 10px 20px; 
                width: 80%; 
                max-width: 300px; 
            }
        }
    </style>
</head>
<body>
    <h1>艾宾浩斯学习计划表</h1>

    <div id="selection-container">
        <h2>设置学习计划</h2>
        <p class="instructions">将下方的学习单元编号拖拽到计划表的"学习内容"栏中进行规划。<br>在各复习时间点手动输入复习的单元编号，并点击"完成"标记。</p>

        <div id="study-plan-setup-area">
            <div id="draggable-numbers-title">可拖拽的学习单元 (1-50, 51-100, ..., 1951-2000):</div>
            <div id="draggable-numbers-container"></div>

            <div id="study-plan-table-container">
                <h3>艾宾浩斯遗忘曲线复习计划表</h3>
                <table id="study-plan-table">
                    <thead>
                        <tr>
                            <th style="width: 40px;">折叠</th>
                            <th>序号</th>
                            <th>学习日期</th>
                            <th>学习内容 (拖入单元)</th>
                            <th>第1天</th>
                            <th>第2天</th>
                            <th>第3天</th>
                            <th>第5天</th>
                            <th>第8天</th>
                            <th>第16天</th>
                            <th>第31天</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <button id="add-plan-row-button">添加新学习日</button>

            <div class="plan-action-buttons-container">
                <button id="print-button">🖨️ 打印学习表</button>
                <button id="save-plan-button">💾 保存学习计划</button>
                <button id="undo-button">↶ 撤销操作</button>
                <button id="reset-plan-button">🗑️ 重置学习计划</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const draggableNumbersContainer = document.getElementById('draggable-numbers-container');
            const studyPlanTableBody = document.querySelector('#study-plan-table tbody');
            const addPlanRowButton = document.getElementById('add-plan-row-button');
            const savePlanButton = document.getElementById('save-plan-button');
            const resetPlanButton = document.getElementById('reset-plan-button');
            const printButton = document.getElementById('print-button');
            const undoButton = document.getElementById('undo-button');

            let draggableNumbersState = {};
            const planTableStorageKey = 'ebbinghausStudyPlan2025_v1';
            const collapseStateStorageKey = 'ebbinghausCollapseState2025_v1';

            // 撤销功能相关变量
            let historyStack = [];
            const maxHistorySize = 20; // 最多保存20步操作历史

            // 折叠状态管理
            let collapseStates = {}; // 存储每个组的折叠状态
            const rowsPerGroup = 10; // 每组10行

            // 生成从7月9日到8月31日的日期数组
            let initialPlanTableStructure = [];
            let seq = 1;

            // 7月9日到7月31日
            for (let day = 9; day <= 31; day++) {
                initialPlanTableStructure.push({ seq: seq++, date: `7月${day}日` });
            }

            // 8月1日到8月31日
            for (let day = 1; day <= 31; day++) {
                initialPlanTableStructure.push({ seq: seq++, date: `8月${day}日` });
            }

            const reviewIntervalHeaders = ["第1天", "第2天", "第3天", "第5天", "第8天", "第16天", "第31天"];

            // 保存当前状态到历史记录
            function saveToHistory() {
                const currentState = getCurrentPlanState();
                historyStack.push(JSON.parse(JSON.stringify(currentState)));

                // 限制历史记录大小
                if (historyStack.length > maxHistorySize) {
                    historyStack.shift();
                }

                updateUndoButtonState();
            }

            // 获取当前计划状态
            function getCurrentPlanState() {
                const planState = {
                    tableData: [],
                    draggableNumbersState: { ...draggableNumbersState }
                };

                studyPlanTableBody.querySelectorAll('tr').forEach(tr => {
                    const rowIndex = parseInt(tr.dataset.rowIndex, 10);
                    const rowData = {
                        seq: parseInt(tr.cells[0].textContent, 10),
                        date: tr.cells[1].querySelector('input.study-date-input').value,
                        studyContent: [],
                        reviewCells: []
                    };

                    tr.cells[2].querySelectorAll('.dropped-item-tag').forEach(tag => {
                        rowData.studyContent.push(tag.dataset.value);
                    });

                    tr.querySelectorAll('.review-cell-content').forEach((rcDiv) => {
                        const input = rcDiv.querySelector('input[type="text"]');
                        const button = rcDiv.querySelector('button');
                        rowData.reviewCells.push({
                            text: input.value,
                            done: button.disabled
                        });
                    });

                    planState.tableData[rowIndex] = rowData;
                });

                planState.tableData = planState.tableData.filter(Boolean);
                return planState;
            }

            // 恢复到指定状态
            function restoreFromState(state) {
                // 恢复拖拽状态
                draggableNumbersState = { ...state.draggableNumbersState };
                generateDraggableNumbers();

                // 恢复表格数据
                const tableDataForRendering = state.tableData.length > 0
                                             ? state.tableData
                                             : JSON.parse(JSON.stringify(initialPlanTableStructure));
                generateStudyPlanTable(tableDataForRendering, state);
            }

            // 撤销操作
            function undoLastOperation() {
                if (historyStack.length > 0) {
                    const previousState = historyStack.pop();
                    restoreFromState(previousState);
                    updateUndoButtonState();
                }
            }

            // 更新撤销按钮状态
            function updateUndoButtonState() {
                undoButton.disabled = historyStack.length === 0;
            }

            // 生成学习单元：1-50, 51-100, ..., 1951-2000 (共40个单元)
            function generateDraggableNumbers() {
                draggableNumbersContainer.innerHTML = '';
                for (let i = 0; i < 40; i++) {
                    const start = i * 50 + 1;
                    const end = (i + 1) * 50;
                    const unitName = `${start}-${end}`;

                    const button = document.createElement('button');
                    button.classList.add('draggable-number');
                    button.textContent = unitName;
                    button.draggable = true;
                    button.dataset.number = unitName;

                    if (draggableNumbersState[unitName]) {
                        button.classList.add('used');
                        button.draggable = false;
                    }

                    button.addEventListener('dragstart', (e) => {
                        if (button.classList.contains('used')) {
                            e.preventDefault();
                            return;
                        }
                        e.dataTransfer.setData('text/plain', unitName);
                        e.dataTransfer.effectAllowed = 'move';
                    });

                    draggableNumbersContainer.appendChild(button);
                }
            }

            function updateDraggableButtonState(number, used) {
                const button = draggableNumbersContainer.querySelector(`.draggable-number[data-number="${number}"]`);
                if (button) {
                    if (used) {
                        button.classList.add('used');
                        button.draggable = false;
                        draggableNumbersState[number] = true;
                    } else {
                        button.classList.remove('used');
                        button.draggable = true;
                        delete draggableNumbersState[number];
                    }
                }
            }

            // 折叠功能相关函数
            function getGroupIndex(rowIndex) {
                return Math.floor(rowIndex / rowsPerGroup);
            }

            function isGroupHeader(rowIndex) {
                return rowIndex % rowsPerGroup === 0;
            }

            function toggleGroupCollapse(groupIndex) {
                const isCollapsed = collapseStates[groupIndex] || false;
                collapseStates[groupIndex] = !isCollapsed;

                // 更新UI
                updateGroupVisibility(groupIndex);
                updateCollapseButton(groupIndex);

                // 保存折叠状态
                saveCollapseStates();
            }

            function updateGroupVisibility(groupIndex) {
                const isCollapsed = collapseStates[groupIndex] || false;
                const startRow = groupIndex * rowsPerGroup;
                const endRow = startRow + rowsPerGroup - 1;

                for (let i = startRow; i <= endRow; i++) {
                    const row = studyPlanTableBody.querySelector(`tr[data-row-index="${i}"]`);
                    if (row && !isGroupHeader(i)) {
                        if (isCollapsed) {
                            row.classList.add('row-group-collapsed');
                        } else {
                            row.classList.remove('row-group-collapsed');
                        }
                    }
                }
            }

            function updateCollapseButton(groupIndex) {
                const isCollapsed = collapseStates[groupIndex] || false;
                const headerRowIndex = groupIndex * rowsPerGroup;
                const headerRow = studyPlanTableBody.querySelector(`tr[data-row-index="${headerRowIndex}"]`);

                if (headerRow) {
                    const collapseButton = headerRow.querySelector('.collapse-toggle');
                    if (collapseButton) {
                        collapseButton.textContent = isCollapsed ? '+' : '-';
                        collapseButton.title = isCollapsed ? '展开此组' : '折叠此组';
                        if (isCollapsed) {
                            collapseButton.classList.add('collapsed');
                        } else {
                            collapseButton.classList.remove('collapsed');
                        }
                    }
                }
            }

            function saveCollapseStates() {
                try {
                    localStorage.setItem(collapseStateStorageKey, JSON.stringify(collapseStates));
                } catch (e) {
                    console.error("Error saving collapse states:", e);
                }
            }

            function loadCollapseStates() {
                try {
                    const saved = localStorage.getItem(collapseStateStorageKey);
                    if (saved) {
                        collapseStates = JSON.parse(saved);
                    }
                } catch (e) {
                    console.error("Error loading collapse states:", e);
                    collapseStates = {};
                }
            }

            function createPlanRowUI(rowData, rowIndex, savedRowSpecificData = null) {
                const tr = document.createElement('tr');
                tr.dataset.rowIndex = rowIndex;

                // 如果是组头行，添加特殊样式
                if (isGroupHeader(rowIndex)) {
                    tr.classList.add('group-header-row');
                }

                // 添加折叠按钮列
                const collapseCell = document.createElement('td');
                if (isGroupHeader(rowIndex)) {
                    const collapseButton = document.createElement('div');
                    collapseButton.classList.add('collapse-toggle');
                    const groupIndex = getGroupIndex(rowIndex);
                    const isCollapsed = collapseStates[groupIndex] || false;
                    collapseButton.textContent = isCollapsed ? '+' : '-';
                    collapseButton.title = isCollapsed ? '展开此组' : '折叠此组';
                    if (isCollapsed) {
                        collapseButton.classList.add('collapsed');
                    }

                    collapseButton.addEventListener('click', () => {
                        toggleGroupCollapse(groupIndex);
                    });

                    collapseCell.appendChild(collapseButton);
                } else {
                    collapseCell.innerHTML = ''; // 非组头行留空
                }
                tr.appendChild(collapseCell);

                const seqCell = document.createElement('td');
                seqCell.textContent = rowData.seq;
                tr.appendChild(seqCell);

                const dateCell = document.createElement('td');
                const dateInput = document.createElement('input');
                dateInput.type = 'text';
                dateInput.classList.add('study-date-input');
                dateInput.value = (savedRowSpecificData && savedRowSpecificData.date !== undefined) ? savedRowSpecificData.date : (rowData.date || "");
                dateInput.placeholder = "日期";
                dateCell.appendChild(dateInput);
                tr.appendChild(dateCell);

                const contentCell = document.createElement('td');
                contentCell.classList.add('study-content-cell');

                if (savedRowSpecificData && savedRowSpecificData.studyContent) {
                    savedRowSpecificData.studyContent.forEach(num => {
                        createAndAppendDroppedItemTag(contentCell, num, true);
                    });
                }

                contentCell.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    contentCell.classList.add('drag-over');
                    e.dataTransfer.dropEffect = 'move';
                });

                contentCell.addEventListener('dragleave', (e) => {
                    contentCell.classList.remove('drag-over');
                });

                contentCell.addEventListener('drop', (e) => {
                    e.preventDefault();
                    contentCell.classList.remove('drag-over');
                    const droppedNumberStr = e.dataTransfer.getData('text/plain');

                    if (draggableNumbersState[droppedNumberStr]) {
                        alert(`学习单元 ${droppedNumberStr} 已被使用。请先从其他单元格移除。`);
                        return;
                    }

                    // 保存操作前状态
                    saveToHistory();

                    createAndAppendDroppedItemTag(contentCell, droppedNumberStr, false);
                    updateDraggableButtonState(droppedNumberStr, true);
                });

                tr.appendChild(contentCell);

                reviewIntervalHeaders.forEach((header, colIndex) => {
                    const reviewTd = document.createElement('td');
                    const reviewContentDiv = document.createElement('div');
                    reviewContentDiv.classList.add('review-cell-content');

                    const input = document.createElement('input');
                    input.type = 'text';
                    input.placeholder = '';

                    const btn = document.createElement('button');
                    btn.textContent = '完成';

                    if (savedRowSpecificData && savedRowSpecificData.reviewCells && savedRowSpecificData.reviewCells[colIndex]) {
                        const cellState = savedRowSpecificData.reviewCells[colIndex];
                        input.value = cellState.text;
                        if (cellState.done) {
                            btn.disabled = true;
                            input.readOnly = true;
                        }
                    }

                    btn.addEventListener('click', function() {
                        // 保存操作前状态
                        saveToHistory();

                        this.disabled = true;
                        input.readOnly = true;
                    });

                    reviewContentDiv.appendChild(input);
                    reviewContentDiv.appendChild(btn);
                    reviewTd.appendChild(reviewContentDiv);
                    tr.appendChild(reviewTd);
                });

                return tr;
            }

            function generateStudyPlanTable(planDataArray = initialPlanTableStructure, savedPlanObject = null) {
                studyPlanTableBody.innerHTML = '';
                planDataArray.forEach((rowData, rowIndex) => {
                    const specificSavedDataForRow = (savedPlanObject && savedPlanObject.tableData && savedPlanObject.tableData[rowIndex])
                                                  ? savedPlanObject.tableData[rowIndex]
                                                  : rowData;
                    const tr = createPlanRowUI(rowData, rowIndex, specificSavedDataForRow);
                    studyPlanTableBody.appendChild(tr);
                });

                // 应用折叠状态
                applyCollapseStates();
            }

            function applyCollapseStates() {
                // 为每个组应用折叠状态
                Object.keys(collapseStates).forEach(groupIndex => {
                    updateGroupVisibility(parseInt(groupIndex));
                    updateCollapseButton(parseInt(groupIndex));
                });
            }

            function addStudyPlanRow() {
                // 保存操作前状态
                saveToHistory();

                const newSeq = studyPlanTableBody.rows.length + 1;
                const newRowData = { seq: newSeq, date: "" };
                const tr = createPlanRowUI(newRowData, studyPlanTableBody.rows.length, null);
                studyPlanTableBody.appendChild(tr);
                tr.scrollIntoView({ behavior: 'smooth', block: 'end' });
            }

            function createAndAppendDroppedItemTag(cell, number, isLoadedFromSave = false) {
                const itemTag = document.createElement('span');
                itemTag.classList.add('dropped-item-tag');
                itemTag.dataset.value = number;
                itemTag.textContent = number;

                const removeBtn = document.createElement('span');
                removeBtn.classList.add('remove-item-btn');
                removeBtn.textContent = 'x';
                removeBtn.title = '移除此单元';
                removeBtn.addEventListener('click', () => {
                    // 保存操作前状态
                    saveToHistory();

                    itemTag.remove();
                    updateDraggableButtonState(number, false);
                });

                itemTag.appendChild(removeBtn);
                cell.appendChild(itemTag);

                if (isLoadedFromSave && !draggableNumbersState[number]) {
                    updateDraggableButtonState(number, true);
                }
            }

            function savePlanState() {
                const planStateToSave = {
                    tableData: [],
                    draggableNumbersState: draggableNumbersState,
                };

                studyPlanTableBody.querySelectorAll('tr').forEach(tr => {
                    const rowIndex = parseInt(tr.dataset.rowIndex, 10);
                    const rowData = {
                        seq: parseInt(tr.cells[0].textContent, 10),
                        date: tr.cells[1].querySelector('input.study-date-input').value,
                        studyContent: [],
                        reviewCells: []
                    };

                    tr.cells[2].querySelectorAll('.dropped-item-tag').forEach(tag => {
                        rowData.studyContent.push(tag.dataset.value);
                    });

                    tr.querySelectorAll('.review-cell-content').forEach((rcDiv) => {
                        const input = rcDiv.querySelector('input[type="text"]');
                        const button = rcDiv.querySelector('button');
                        rowData.reviewCells.push({
                            text: input.value,
                            done: button.disabled
                        });
                    });

                    planStateToSave.tableData[rowIndex] = rowData;
                });

                planStateToSave.tableData = planStateToSave.tableData.filter(Boolean);

                try {
                    localStorage.setItem(planTableStorageKey, JSON.stringify(planStateToSave));
                    alert('学习计划已保存！');
                } catch (e) {
                    console.error("Error saving plan to localStorage:", e);
                    alert('学习计划保存失败。');
                }
            }

            function loadPlanState() {
                const savedDataString = localStorage.getItem(planTableStorageKey);
                if (savedDataString) {
                    try {
                        const savedData = JSON.parse(savedDataString);

                        if (savedData.draggableNumbersState) {
                            Object.assign(draggableNumbersState, savedData.draggableNumbersState);
                        } else {
                            draggableNumbersState = {};
                        }
                        generateDraggableNumbers();

                        const tableDataForRendering = (savedData.tableData && savedData.tableData.length > 0)
                                                     ? savedData.tableData
                                                     : JSON.parse(JSON.stringify(initialPlanTableStructure));
                        generateStudyPlanTable(tableDataForRendering, savedData);
                        console.log("学习计划已加载。");
                    } catch (e) {
                        console.error("Error loading or parsing saved plan data:", e);
                        resetPlanUI(false);
                    }
                } else {
                    generateDraggableNumbers();
                    generateStudyPlanTable(JSON.parse(JSON.stringify(initialPlanTableStructure)));
                }
            }

            function resetPlanUI(showAlert = true) {
                if (showAlert && !confirm("确定要重置学习计划表吗？此操作不可撤销。")) {
                    return;
                }
                localStorage.removeItem(planTableStorageKey);
                localStorage.removeItem(collapseStateStorageKey);

                draggableNumbersState = {};
                collapseStates = {};
                generateDraggableNumbers();

                let freshInitialPlan = JSON.parse(JSON.stringify(initialPlanTableStructure));
                generateStudyPlanTable(freshInitialPlan);

                if (showAlert) alert("学习计划表已重置。");
            }

            // 打印功能
            function printPlan() {
                window.print();
            }

            // 事件监听器
            addPlanRowButton.addEventListener('click', addStudyPlanRow);
            savePlanButton.addEventListener('click', savePlanState);
            resetPlanButton.addEventListener('click', resetPlanUI);
            printButton.addEventListener('click', printPlan);
            undoButton.addEventListener('click', undoLastOperation);

            // 自动填充学习内容
            function autoFillStudyContent() {
                const rows = studyPlanTableBody.querySelectorAll('tr');
                let unitIndex = 0;

                rows.forEach((row, rowIndex) => {
                    const contentCell = row.cells[2]; // 学习内容栏是第3列（索引2）

                    // 检查是否已经有内容，如果没有则自动添加
                    if (contentCell && contentCell.querySelectorAll('.dropped-item-tag').length === 0 && unitIndex < 40) {
                        const start = unitIndex * 50 + 1;
                        const end = (unitIndex + 1) * 50;
                        const unitName = `${start}-${end}`;

                        // 创建并添加学习单元标签
                        createAndAppendDroppedItemTag(contentCell, unitName, false);
                        updateDraggableButtonState(unitName, true);

                        unitIndex++;
                    }
                });
            }

            // 自动填充第1天复习内容
            function autoFillFirstDayReview() {
                const rows = studyPlanTableBody.querySelectorAll('tr');

                rows.forEach((row, rowIndex) => {
                    if (rowIndex < 40) { // 只填充前40行
                        const firstDayCell = row.cells[3]; // "第1天"是第4列（索引3）
                        if (firstDayCell) {
                            const reviewContent = firstDayCell.querySelector('.review-cell-content');
                            if (reviewContent) {
                                const input = reviewContent.querySelector('input[type="text"]');
                                if (input && !input.value) {
                                    input.value = (rowIndex + 1).toString(); // 填入1-40
                                }
                            }
                        }
                    }
                });
            }

            // 初始化页面
            loadCollapseStates(); // 先加载折叠状态
            loadPlanState();

            // 初始化撤销按钮状态
            updateUndoButtonState();

            // 如果是首次加载且没有保存的数据，自动填充学习内容
            setTimeout(() => {
                const savedDataString = localStorage.getItem(planTableStorageKey);
                if (!savedDataString) {
                    autoFillStudyContent();
                    autoFillFirstDayReview();
                    // 自动填充后保存初始状态
                    setTimeout(() => {
                        saveToHistory();
                    }, 50);
                }
            }, 100);
        });
    </script>
</body>
</html>
